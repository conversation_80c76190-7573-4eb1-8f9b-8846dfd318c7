# -*- coding: utf-8 -*-
"""
سكريبت تثبيت وإعداد برنامج الوعد الصادق 4
"""

import sys
import subprocess
import os
from pathlib import Path

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 6):
        print("❌ خطأ: يتطلب البرنامج Python 3.6 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ إصدار Python مناسب: {sys.version}")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 جاري تثبيت المتطلبات...")
    
    try:
        # تحديث pip أولاً
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # تثبيت المتطلبات
        requirements_file = Path(__file__).parent / "requirements.txt"
        if requirements_file.exists():
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)])
            print("✅ تم تثبيت جميع المتطلبات بنجاح")
            return True
        else:
            print("❌ ملف requirements.txt غير موجود")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    print("📁 جاري إنشاء المجلدات...")
    
    base_dir = Path(__file__).parent
    directories = [
        "data",
        "backups", 
        "reports",
        "resources",
        "logs"
    ]
    
    for directory in directories:
        dir_path = base_dir / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ تم إنشاء مجلد: {directory}")

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب (Windows)"""
    if sys.platform != "win32":
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "الوعد الصادق 4.lnk")
        target = sys.executable
        wDir = str(Path(__file__).parent)
        arguments = str(Path(__file__).parent / "main.py")
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = target
        shortcut.Arguments = f'"{arguments}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = target
        shortcut.save()
        
        print("✅ تم إنشاء اختصار على سطح المكتب")
        
    except ImportError:
        print("⚠️ لم يتم إنشاء اختصار سطح المكتب (مكتبات Windows غير متوفرة)")
    except Exception as e:
        print(f"⚠️ لم يتم إنشاء اختصار سطح المكتب: {e}")

def test_installation():
    """اختبار التثبيت"""
    print("🧪 جاري اختبار التثبيت...")
    
    try:
        # اختبار استيراد المكتبات الأساسية
        import PyQt5
        print("✅ PyQt5 متوفر")
        
        import sqlite3
        print("✅ SQLite متوفر")
        
        import bcrypt
        print("✅ bcrypt متوفر")
        
        import reportlab
        print("✅ ReportLab متوفر")
        
        # اختبار إنشاء قاعدة البيانات
        from database.database_manager import db_manager
        print("✅ قاعدة البيانات جاهزة")
        
        print("✅ جميع الاختبارات نجحت!")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية للتثبيت"""
    print("=" * 50)
    print("🚀 مرحباً بك في برنامج تثبيت الوعد الصادق 4")
    print("=" * 50)
    
    # التحقق من إصدار Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        input("اضغط Enter للخروج...")
        return
    
    # إنشاء المجلدات
    create_directories()
    
    # إنشاء اختصار سطح المكتب
    create_desktop_shortcut()
    
    # اختبار التثبيت
    if test_installation():
        print("\n" + "=" * 50)
        print("🎉 تم تثبيت البرنامج بنجاح!")
        print("=" * 50)
        print("\n📋 بيانات تسجيل الدخول الافتراضية:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("\n⚠️  تنبيه: يُنصح بتغيير كلمة المرور فور تسجيل الدخول الأول")
        print("\n🚀 لتشغيل البرنامج:")
        print("   python main.py")
        print("\n📖 لمزيد من المعلومات، راجع ملف README.md")
        
        # سؤال المستخدم إذا كان يريد تشغيل البرنامج الآن
        response = input("\nهل تريد تشغيل البرنامج الآن؟ (y/n): ")
        if response.lower() in ['y', 'yes', 'نعم', 'ن']:
            try:
                subprocess.Popen([sys.executable, "main.py"])
                print("✅ تم تشغيل البرنامج")
            except Exception as e:
                print(f"❌ خطأ في تشغيل البرنامج: {e}")
    else:
        print("\n❌ فشل في التثبيت. يرجى مراجعة الأخطاء أعلاه.")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
