@echo off
chcp 65001 > nul
title بناء الوعد الصادق 4 - نسخة مبسطة

echo ================================================
echo 🏗️ بناء الوعد الصادق 4 كتطبيق احترافي
echo ================================================
echo.

echo 📋 جاري إنشاء هيكل التطبيق الاحترافي...

REM إنشاء مجلد التوزيع
if not exist "release" mkdir release
if not exist "release\الوعد الصادق 4" mkdir "release\الوعد الصادق 4"
if not exist "release\الوعد الصادق 4\data" mkdir "release\الوعد الصادق 4\data"
if not exist "release\الوعد الصادق 4\backups" mkdir "release\الوعد الصادق 4\backups"
if not exist "release\الوعد الصادق 4\reports" mkdir "release\الوعد الصادق 4\reports"
if not exist "release\الوعد الصادق 4\resources" mkdir "release\الوعد الصادق 4\resources"
if not exist "release\الوعد الصادق 4\config" mkdir "release\الوعد الصادق 4\config"
if not exist "release\الوعد الصادق 4\database" mkdir "release\الوعد الصادق 4\database"
if not exist "release\الوعد الصادق 4\models" mkdir "release\الوعد الصادق 4\models"
if not exist "release\الوعد الصادق 4\ui" mkdir "release\الوعد الصادق 4\ui"
if not exist "release\الوعد الصادق 4\utils" mkdir "release\الوعد الصادق 4\utils"

echo ✅ تم إنشاء هيكل المجلدات

echo 📄 جاري نسخ ملفات البرنامج...

REM نسخ الملفات الرئيسية
copy "main.py" "release\الوعد الصادق 4\" > nul 2>&1
copy "console_app.py" "release\الوعد الصادق 4\" > nul 2>&1
copy "requirements.txt" "release\الوعد الصادق 4\" > nul 2>&1

REM نسخ المجلدات
xcopy /E /I /Y "config\*" "release\الوعد الصادق 4\config\" > nul 2>&1
xcopy /E /I /Y "database\*" "release\الوعد الصادق 4\database\" > nul 2>&1
xcopy /E /I /Y "models\*" "release\الوعد الصادق 4\models\" > nul 2>&1
xcopy /E /I /Y "ui\*" "release\الوعد الصادق 4\ui\" > nul 2>&1
xcopy /E /I /Y "utils\*" "release\الوعد الصادق 4\utils\" > nul 2>&1

REM نسخ ملفات التوثيق
copy "README.md" "release\الوعد الصادق 4\" > nul 2>&1
copy "INSTALLATION_GUIDE.md" "release\الوعد الصادق 4\" > nul 2>&1
copy "PROJECT_SUMMARY.md" "release\الوعد الصادق 4\" > nul 2>&1
copy "BUILD_GUIDE.md" "release\الوعد الصادق 4\" > nul 2>&1
copy "LICENSE.txt" "release\الوعد الصادق 4\" > nul 2>&1

echo ✅ تم نسخ ملفات البرنامج

echo 🔧 جاري إنشاء ملفات التشغيل...

REM إنشاء ملف تشغيل البرنامج الرئيسي
(
echo @echo off
echo chcp 65001 ^> nul
echo title الوعد الصادق 4
echo cd /d "%%~dp0"
echo python main.py
echo if errorlevel 1 ^(
echo     echo ❌ خطأ في تشغيل البرنامج
echo     echo 📋 تأكد من تثبيت Python والمتطلبات
echo     echo 🔧 شغل: python -m pip install -r requirements.txt
echo     pause
echo ^)
) > "release\الوعد الصادق 4\تشغيل البرنامج.bat"

REM إنشاء ملف تشغيل نسخة سطر الأوامر
(
echo @echo off
echo chcp 65001 ^> nul
echo title الوعد الصادق 4 - سطر الأوامر
echo cd /d "%%~dp0"
echo python console_app.py
echo if errorlevel 1 ^(
echo     echo ❌ خطأ في تشغيل البرنامج
echo     echo 📋 تأكد من تثبيت Python والمتطلبات
echo     pause
echo ^)
) > "release\الوعد الصادق 4\تشغيل سطر الأوامر.bat"

REM إنشاء ملف تثبيت المتطلبات
(
echo @echo off
echo chcp 65001 ^> nul
echo title تثبيت متطلبات الوعد الصادق 4
echo cd /d "%%~dp0"
echo echo 📦 جاري تثبيت المتطلبات...
echo python -m pip install --upgrade pip
echo python -m pip install -r requirements.txt
echo if errorlevel 1 ^(
echo     echo ❌ فشل في تثبيت المتطلبات
echo     echo 🔧 تأكد من اتصال الإنترنت وصحة ملف requirements.txt
echo ^) else ^(
echo     echo ✅ تم تثبيت المتطلبات بنجاح
echo     echo 🚀 يمكنك الآن تشغيل البرنامج
echo ^)
echo pause
) > "release\الوعد الصادق 4\تثبيت المتطلبات.bat"

echo ✅ تم إنشاء ملفات التشغيل

echo 📝 جاري إنشاء ملف التثبيت الاحترافي...

REM إنشاء ملف التثبيت الاحترافي
(
echo @echo off
echo chcp 65001 ^> nul
echo title تثبيت الوعد الصادق 4
echo.
echo ================================================
echo 🚀 مرحباً بك في برنامج تثبيت الوعد الصادق 4
echo ================================================
echo.
echo 📋 معلومات البرنامج:
echo    الاسم: الوعد الصادق 4
echo    الإصدار: 4.0.0
echo    النوع: نظام إدارة المبيعات والتقسيط
echo    المطور: فريق الوعد الصادق
echo.
echo 🔍 جاري التحقق من Python...
echo.
python --version ^>nul 2^>^&1
if errorlevel 1 ^(
echo     ❌ Python غير مثبت على هذا الجهاز
echo     📥 يرجى تحميل وتثبيت Python من:
echo     https://www.python.org/downloads/
echo     ⚠️ تأكد من تفعيل خيار "Add Python to PATH"
echo     pause
echo     exit /b 1
^)
echo ✅ Python متوفر
echo.
set /p install_path="📁 مجلد التثبيت ^(اتركه فارغاً للمجلد الافتراضي^): "
if "%%install_path%%"=="" set install_path=C:\Program Files\الوعد الصادق 4
echo.
echo 📁 سيتم تثبيت البرنامج في: %%install_path%%
echo.
set /p confirm="هل تريد المتابعة؟ ^(y/n^): "
if /i not "%%confirm%%"=="y" ^(
echo     ❌ تم إلغاء التثبيت
echo     pause
echo     exit /b 1
^)
echo.
echo 📦 جاري التثبيت...
echo.
if not exist "%%install_path%%" mkdir "%%install_path%%"
echo 📄 جاري نسخ الملفات...
xcopy /E /I /Y "الوعد الصادق 4\*" "%%install_path%%\" ^> nul
if errorlevel 1 ^(
echo     ❌ فشل في نسخ الملفات
echo     pause
echo     exit /b 1
^)
echo ✅ تم نسخ الملفات
echo.
echo 📦 جاري تثبيت المتطلبات...
cd /d "%%install_path%%"
python -m pip install -r requirements.txt ^>nul 2^>^&1
if errorlevel 1 ^(
echo     ⚠️ فشل في تثبيت بعض المتطلبات
echo     💡 يمكنك تثبيتها لاحقاً بتشغيل "تثبيت المتطلبات.bat"
^) else ^(
echo     ✅ تم تثبيت المتطلبات
^)
echo.
echo 🔗 جاري إنشاء الاختصارات...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut^('%%USERPROFILE%%\Desktop\الوعد الصادق 4.lnk'^); $Shortcut.TargetPath = '%%install_path%%\تشغيل البرنامج.bat'; $Shortcut.WorkingDirectory = '%%install_path%%'; $Shortcut.Save^(^)" ^>nul 2^>^&1
if not errorlevel 1 echo ✅ تم إنشاء اختصار سطح المكتب
echo.
echo ================================================
echo ✅ تم تثبيت البرنامج بنجاح!
echo ================================================
echo.
echo 📁 مجلد التثبيت: %%install_path%%
echo 🔗 اختصار سطح المكتب: تم إنشاؤه
echo.
echo 🚀 يمكنك الآن تشغيل البرنامج من:
echo    - اختصار سطح المكتب
echo    - الملف: %%install_path%%\تشغيل البرنامج.bat
echo.
echo 📋 بيانات تسجيل الدخول الافتراضية:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.
set /p run_now="هل تريد تشغيل البرنامج الآن؟ ^(y/n^): "
if /i "%%run_now%%"=="y" ^(
echo     🚀 جاري تشغيل البرنامج...
echo     start "" "%%install_path%%\تشغيل البرنامج.bat"
^)
echo.
echo 🎉 شكراً لاستخدام الوعد الصادق 4!
pause
) > "release\تثبيت الوعد الصادق 4.bat"

echo ✅ تم إنشاء ملف التثبيت

echo 📝 جاري إنشاء ملف معلومات الإصدار...

REM إنشاء ملف معلومات الإصدار
(
echo الوعد الصادق 4 - الإصدار 4.0.0
echo ================================
echo.
echo 📅 تاريخ البناء: %date% %time%
echo 🏗️ نوع البناء: تطبيق Python احترافي
echo 💻 متوافق مع: Windows 7/8/10/11
echo.
echo 📦 محتويات الحزمة:
echo ==================
echo 📁 الوعد الصادق 4\          - مجلد البرنامج الرئيسي
echo    ├── main.py               - الملف الرئيسي ^(واجهة رسومية^)
echo    ├── console_app.py        - نسخة سطر الأوامر
echo    ├── تشغيل البرنامج.bat    - ملف تشغيل سريع
echo    ├── تشغيل سطر الأوامر.bat - تشغيل نسخة الكونسول
echo    ├── تثبيت المتطلبات.bat   - تثبيت المكتبات المطلوبة
echo    ├── config\               - ملفات الإعدادات
echo    ├── database\             - إدارة قاعدة البيانات
echo    ├── models\               - نماذج البيانات
echo    ├── ui\                   - واجهات المستخدم
echo    ├── utils\                - أدوات مساعدة
echo    ├── data\                 - مجلد البيانات ^(يتم إنشاؤه تلقائياً^)
echo    ├── backups\              - مجلد النسخ الاحتياطية
echo    ├── reports\              - مجلد التقارير
echo    └── ملفات التوثيق والمساعدة
echo.
echo 📋 تثبيت الوعد الصادق 4.bat - ملف التثبيت التلقائي
echo.
echo 🚀 طرق التشغيل:
echo ===============
echo 1. التثبيت التلقائي: شغل "تثبيت الوعد الصادق 4.bat"
echo 2. التشغيل المباشر: ادخل مجلد البرنامج وشغل "تشغيل البرنامج.bat"
echo 3. سطر الأوامر: شغل "تشغيل سطر الأوامر.bat"
echo.
echo 📋 بيانات تسجيل الدخول الافتراضية:
echo ===================================
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo ⚠️ تنبيه: يُنصح بتغيير كلمة المرور فور تسجيل الدخول الأول
echo.
echo 🔧 متطلبات النظام:
echo ==================
echo - Windows 7 أو أحدث
echo - Python 3.6 أو أحدث
echo - 4 جيجابايت RAM ^(الحد الأدنى^)
echo - 500 ميجابايت مساحة فارغة
echo - اتصال إنترنت ^(لتثبيت المتطلبات^)
echo.
echo 📞 الدعم الفني:
echo ===============
echo البريد الإلكتروني: <EMAIL>
echo الهاتف: +966 XX XXX XXXX
echo الموقع: www.alwaad.com
echo.
echo 📖 للمزيد من المعلومات:
echo =========================
echo - README.md - دليل المستخدم الشامل
echo - INSTALLATION_GUIDE.md - دليل التثبيت المفصل
echo - BUILD_GUIDE.md - دليل البناء والتطوير
echo - PROJECT_SUMMARY.md - ملخص المشروع
echo.
echo © 2024 فريق الوعد الصادق. جميع الحقوق محفوظة.
) > "release\معلومات الإصدار.txt"

echo ✅ تم إنشاء ملف معلومات الإصدار

echo.
echo 📊 إحصائيات البناء:
echo ===================

REM حساب عدد الملفات
for /f %%i in ('dir /s /b "release\الوعد الصادق 4\*.*" ^| find /c /v ""') do set file_count=%%i
echo 📄 عدد الملفات: %file_count%

REM حساب حجم المجلد
for /f "tokens=3" %%i in ('dir /s "release\الوعد الصادق 4" ^| find "File(s)"') do set total_size=%%i
echo 📦 الحجم الإجمالي: %total_size% بايت

echo 📁 مجلد الإخراج: release\

echo.
echo ================================================
echo 🎉 تم بناء التطبيق الاحترافي بنجاح!
echo ================================================
echo.
echo 📁 الملفات الناتجة:
echo ===================
echo 📦 release\الوعد الصادق 4\ - مجلد التطبيق الكامل
echo 🔧 release\تثبيت الوعد الصادق 4.bat - ملف التثبيت التلقائي
echo 📝 release\معلومات الإصدار.txt - معلومات مفصلة
echo.
echo 🚀 يمكنك الآن:
echo ==============
echo 1. نسخ مجلد release\ إلى أي جهاز آخر
echo 2. تشغيل "تثبيت الوعد الصادق 4.bat" للتثبيت التلقائي
echo 3. توزيع البرنامج كتطبيق احترافي
echo 4. ضغط المجلد وإرساله عبر الإنترنت
echo.
echo 📋 ملاحظات مهمة:
echo ================
echo - تأكد من وجود Python على الجهاز المستهدف
echo - البرنامج يعمل بدون تثبيت إضافي بعد نسخ الملفات
echo - جميع البيانات محفوظة محلياً في مجلد data\
echo.

set /p open_folder="هل تريد فتح مجلد الإخراج؟ (y/n): "
if /i "%open_folder%"=="y" (
    explorer release
)

echo.
echo 🎉 شكراً لاستخدام الوعد الصادق 4!
echo 💼 البرنامج جاهز للاستخدام التجاري والتوزيع
pause
