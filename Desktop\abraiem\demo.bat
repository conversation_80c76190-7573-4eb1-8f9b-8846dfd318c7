@echo off
chcp 65001 > nul
title الوعد الصادق 4 - عرض توضيحي

echo ================================================
echo 🚀 برنامج الوعد الصادق 4 - عرض توضيحي
echo ================================================
echo.

echo 🔍 جاري التحقق من Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير متوفر
    echo.
    echo 📋 عرض البيانات يدوياً:
    echo ================================
    echo 🔐 تسجيل الدخول:
    echo    اسم المستخدم: admin
    echo    كلمة المرور: admin123
    echo    ✅ تم تسجيل الدخول بنجاح!
    echo.
    echo 📦 المنتجات المتوفرة:
    echo    1. لابتوب HP - 3500 ريال - مخزون: 10
    echo    2. هاتف Samsung - 1200 ريال - مخزون: 25  
    echo    3. طاولة مكتب - 800 ريال - مخزون: 5
    echo.
    echo 👥 العملاء المسجلين:
    echo    1. أحمد محمد - 0501234567
    echo    2. فاطمة علي - 0509876543
    echo    3. محمد سعد - 0551122334
    echo.
    echo ✨ ميزات البرنامج:
    echo    ✅ تسجيل دخول آمن
    echo    ✅ إدارة المنتجات والمخزون
    echo    ✅ إدارة العملاء
    echo    ✅ نظام المبيعات ^(نقدي وتقسيط^)
    echo    ✅ إدارة الأقساط
    echo    ✅ فواتير PDF
    echo    ✅ نسخ احتياطية
    echo    ✅ تقارير وإحصائيات
    echo    ✅ دعم اللغة العربية
    echo.
    echo 🎉 البرنامج جاهز للاستخدام!
    echo.
    pause
    exit /b 0
)

echo ✅ Python متوفر
echo.
echo 🚀 جاري تشغيل العرض التوضيحي...
echo.

python quick_demo.py

if errorlevel 1 (
    echo.
    echo ⚠️ حدث خطأ في تشغيل Python
    echo 📋 عرض البيانات يدوياً بدلاً من ذلك:
    echo.
    goto manual_demo
)

echo.
echo 🎉 انتهى العرض التوضيحي بنجاح!
goto end

:manual_demo
echo ================================
echo 🔐 تسجيل الدخول:
echo    اسم المستخدم: admin  
echo    كلمة المرور: admin123
echo    ✅ تم تسجيل الدخول بنجاح!
echo.
echo 📦 المنتجات المتوفرة:
echo    الرقم  الاسم                السعر      المخزون
echo    ----  ------------------  --------  --------
echo    1     لابتوب HP           3500.00   10
echo    2     هاتف Samsung        1200.00   25
echo    3     طاولة مكتب          800.00    5
echo.
echo    إجمالي المنتجات: 3
echo.
echo 👥 العملاء المسجلين:
echo    الرقم  الاسم                الهاتف
echo    ----  ------------------  -------------
echo    1     أحمد محمد           0501234567
echo    2     فاطمة علي           0509876543  
echo    3     محمد سعد            0551122334
echo.
echo    إجمالي العملاء: 3
echo.
echo 📊 إحصائيات البرنامج:
echo    📦 عدد المنتجات: 3
echo    👥 عدد العملاء: 3
echo    💰 إجمالي قيمة المخزون: 41,500 ريال
echo    📅 تاريخ الإنشاء: %date% %time%
echo.
echo ✨ ميزات البرنامج:
echo    ✅ تسجيل دخول آمن
echo    ✅ إدارة المنتجات والمخزون
echo    ✅ إدارة العملاء  
echo    ✅ نظام المبيعات ^(نقدي وتقسيط^)
echo    ✅ إدارة الأقساط
echo    ✅ فواتير PDF
echo    ✅ نسخ احتياطية
echo    ✅ تقارير وإحصائيات
echo    ✅ دعم اللغة العربية
echo.
echo 🎉 البرنامج جاهز للاستخدام!

:end
echo.
echo 📖 لمزيد من المعلومات:
echo    - README.md
echo    - INSTALLATION_GUIDE.md
echo    - PROJECT_SUMMARY.md
echo.
echo 🚀 لتشغيل البرنامج الكامل:
echo    python main.py
echo.
pause
