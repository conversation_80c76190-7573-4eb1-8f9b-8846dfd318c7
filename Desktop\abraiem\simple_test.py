#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3
import os
from pathlib import Path

print("🚀 اختبار بسيط لبرنامج الوعد الصادق 4")
print("=" * 50)

# إنشاء مجلد البيانات
data_dir = Path("data")
data_dir.mkdir(exist_ok=True)
print("✅ تم إنشاء مجلد البيانات")

# إنشاء قاعدة بيانات بسيطة
db_path = data_dir / "test.db"

try:
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    # إنشاء جدول المستخدمين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY,
            username TEXT UNIQUE,
            password TEXT,
            name TEXT
        )
    ''')
    
    # إضافة مستخدم تجريبي
    cursor.execute('''
        INSERT OR REPLACE INTO users (username, password, name)
        VALUES (?, ?, ?)
    ''', ("admin", "admin123", "المدير"))
    
    conn.commit()
    print("✅ تم إنشاء قاعدة البيانات وإضافة مستخدم تجريبي")
    
    # اختبار استرجاع البيانات
    cursor.execute("SELECT * FROM users WHERE username = ?", ("admin",))
    user = cursor.fetchone()
    
    if user:
        print(f"✅ تم العثور على المستخدم: {user[3]} (اسم المستخدم: {user[1]})")
    else:
        print("❌ لم يتم العثور على المستخدم")
    
    conn.close()
    
    print("\n🎉 الاختبار نجح! البرنامج الأساسي يعمل بشكل صحيح")
    print("\n📋 بيانات تسجيل الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    
    print(f"\n📁 ملف قاعدة البيانات: {db_path}")
    print(f"📊 حجم قاعدة البيانات: {db_path.stat().st_size} بايت")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()

print("=" * 50)
