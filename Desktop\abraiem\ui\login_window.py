# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول لبرنامج الوعد الصادق 4
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                            QMessageBox, QFrame, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPalette, QColor
from models.user_model import UserModel

class LoginWindow(QMainWindow):
    login_successful = pyqtSignal(dict)  # إشارة نجاح تسجيل الدخول
    
    def __init__(self):
        super().__init__()
        self.user_model = UserModel()
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("الوعد الصادق 4 - تسجيل الدخول")
        self.setFixedSize(400, 500)
        self.setStyleSheet(self.get_stylesheet())
        
        # الحاوية الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setAlignment(Qt.AlignCenter)
        main_layout.setSpacing(20)
        
        # إطار تسجيل الدخول
        login_frame = QFrame()
        login_frame.setObjectName("loginFrame")
        login_frame.setFixedSize(350, 400)
        
        frame_layout = QVBoxLayout(login_frame)
        frame_layout.setSpacing(20)
        frame_layout.setContentsMargins(30, 30, 30, 30)
        
        # شعار البرنامج
        logo_label = QLabel("الوعد الصادق 4")
        logo_label.setObjectName("logoLabel")
        logo_label.setAlignment(Qt.AlignCenter)
        frame_layout.addWidget(logo_label)
        
        # عنوان فرعي
        subtitle_label = QLabel("نظام إدارة المبيعات والتقسيط")
        subtitle_label.setObjectName("subtitleLabel")
        subtitle_label.setAlignment(Qt.AlignCenter)
        frame_layout.addWidget(subtitle_label)
        
        # مساحة فارغة
        frame_layout.addStretch()
        
        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        username_label.setObjectName("fieldLabel")
        frame_layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setObjectName("inputField")
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setText("admin")  # للاختبار
        frame_layout.addWidget(self.username_input)
        
        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        password_label.setObjectName("fieldLabel")
        frame_layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setObjectName("inputField")
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setText("admin123")  # للاختبار
        frame_layout.addWidget(self.password_input)
        
        # خيار تذكر كلمة المرور
        self.remember_checkbox = QCheckBox("تذكر كلمة المرور")
        self.remember_checkbox.setObjectName("checkBox")
        frame_layout.addWidget(self.remember_checkbox)
        
        # مساحة فارغة
        frame_layout.addStretch()
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setObjectName("loginButton")
        self.login_button.clicked.connect(self.handle_login)
        frame_layout.addWidget(self.login_button)
        
        # ربط Enter بتسجيل الدخول
        self.password_input.returnPressed.connect(self.handle_login)
        self.username_input.returnPressed.connect(self.handle_login)
        
        main_layout.addWidget(login_frame)
        
        # معلومات البرنامج
        info_label = QLabel("© 2024 الوعد الصادق 4 - جميع الحقوق محفوظة")
        info_label.setObjectName("infoLabel")
        info_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(info_label)
        
        # تركيز على حقل اسم المستخدم
        self.username_input.setFocus()
        
    def handle_login(self):
        """معالجة تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        try:
            # التحقق من صحة البيانات
            user = self.user_model.authenticate_user(username, password)
            
            if user:
                # إرسال إشارة نجاح تسجيل الدخول
                self.login_successful.emit(user)
                self.close()
            else:
                QMessageBox.warning(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
                self.password_input.clear()
                self.password_input.setFocus()
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تسجيل الدخول:\n{str(e)}")
    
    def get_stylesheet(self):
        """تنسيق CSS للنافذة"""
        return """
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
            }
            
            #loginFrame {
                background-color: white;
                border-radius: 15px;
                border: 1px solid #ddd;
            }
            
            #logoLabel {
                font-size: 28px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
            
            #subtitleLabel {
                font-size: 14px;
                color: #7f8c8d;
                margin-bottom: 20px;
            }
            
            #fieldLabel {
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 5px;
            }
            
            #inputField {
                padding: 12px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                font-size: 14px;
                background-color: #f8f9fa;
            }
            
            #inputField:focus {
                border-color: #3498db;
                background-color: white;
            }
            
            #checkBox {
                font-size: 12px;
                color: #7f8c8d;
            }
            
            #loginButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
            }
            
            #loginButton:hover {
                background-color: #2980b9;
            }
            
            #loginButton:pressed {
                background-color: #21618c;
            }
            
            #infoLabel {
                font-size: 10px;
                color: white;
                margin-top: 10px;
            }
        """

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    font = QFont("Segoe UI", 10)
    app.setFont(font)
    
    # تعيين اتجاه النص من اليمين لليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = LoginWindow()
    window.show()
    
    sys.exit(app.exec_())
