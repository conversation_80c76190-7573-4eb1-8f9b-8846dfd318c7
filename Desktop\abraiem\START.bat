@echo off
chcp 65001 > nul
title الوعد الصادق 4 - تشغيل ذكي

color 0A
echo.
echo     ██╗    ██╗ ██████╗ ███████╗██████╗ 
echo     ██║    ██║██╔═══██╗██╔════╝██╔══██╗
echo     ██║ █╗ ██║██║   ██║█████╗  ██║  ██║
echo     ██║███╗██║██║   ██║██╔══╝  ██║  ██║
echo     ╚███╔███╔╝╚██████╔╝███████╗██████╔╝
echo      ╚══╝╚══╝  ╚═════╝ ╚══════╝╚═════╝ 
echo.
echo ================================================
echo 🚀 الوعد الصادق 4 - تشغيل ذكي وتلقائي
echo ================================================
echo.
echo 📋 نظام إدارة المبيعات والتقسيط
echo 🏢 إصدار احترافي - الإصدار 4.0
echo.

echo 🔍 جاري فحص النظام والبحث عن أفضل طريقة تشغيل...
echo.

REM محاولة 1: التحقق من Python
echo [1/5] فحص Python...
python --version > nul 2>&1
if not errorlevel 1 (
    echo ✅ Python متوفر
    set python_available=1
) else (
    echo ❌ Python غير متوفر
    set python_available=0
)

REM محاولة 2: التحقق من PyQt5
if %python_available%==1 (
    echo [2/5] فحص PyQt5...
    python -c "import PyQt5" > nul 2>&1
    if not errorlevel 1 (
        echo ✅ PyQt5 متوفر - سيتم تشغيل الواجهة الرسومية
        set gui_available=1
    ) else (
        echo ⚠️ PyQt5 غير متوفر - سيتم تشغيل نسخة الكونسول
        set gui_available=0
    )
) else (
    set gui_available=0
)

REM محاولة 3: التحقق من الملفات
echo [3/5] فحص ملفات البرنامج...
if exist "main.py" (
    echo ✅ الملف الرئيسي موجود
    set main_file=1
) else (
    echo ❌ الملف الرئيسي غير موجود
    set main_file=0
)

if exist "تشغيل_مبسط.py" (
    echo ✅ النسخة المبسطة موجودة
    set simple_file=1
) else (
    echo ❌ النسخة المبسطة غير موجودة
    set simple_file=0
)

REM محاولة 4: التحقق من قاعدة البيانات
echo [4/5] فحص قاعدة البيانات...
if exist "data" (
    echo ✅ مجلد البيانات موجود
) else (
    echo 📁 إنشاء مجلد البيانات...
    mkdir data
)

REM محاولة 5: اختيار طريقة التشغيل
echo [5/5] اختيار طريقة التشغيل المثلى...
echo.

if %python_available%==1 if %gui_available%==1 if %main_file%==1 (
    echo 🎯 الطريقة المختارة: الواجهة الرسومية الكاملة
    echo 🚀 جاري تشغيل البرنامج...
    echo.
    python main.py
    if not errorlevel 1 goto success
    echo ⚠️ فشل في تشغيل الواجهة الرسومية، جاري المحاولة التالية...
)

if %python_available%==1 if %simple_file%==1 (
    echo 🎯 الطريقة المختارة: النسخة المبسطة التفاعلية
    echo 🚀 جاري تشغيل النسخة المبسطة...
    echo.
    python تشغيل_مبسط.py
    if not errorlevel 1 goto success
    echo ⚠️ فشل في تشغيل النسخة المبسطة، جاري المحاولة التالية...
)

if %python_available%==1 (
    echo 🎯 الطريقة المختارة: النسخة المدمجة
    echo 🚀 جاري تشغيل النسخة المدمجة...
    echo.
    python -c "
import sqlite3
import os
from datetime import datetime

print('🚀 الوعد الصادق 4 - نسخة سريعة')
print('=' * 50)

# إنشاء قاعدة بيانات سريعة
os.makedirs('data', exist_ok=True)
conn = sqlite3.connect('data/quick.db')
cursor = conn.cursor()

# إنشاء جداول
cursor.execute('CREATE TABLE IF NOT EXISTS products (id INTEGER PRIMARY KEY, name TEXT, price REAL, stock INTEGER)')
cursor.execute('CREATE TABLE IF NOT EXISTS customers (id INTEGER PRIMARY KEY, name TEXT, phone TEXT)')

# إضافة بيانات
products = [
    (1, 'لابتوب HP', 3500.0, 10),
    (2, 'هاتف Samsung', 1200.0, 25),
    (3, 'طاولة مكتب', 800.0, 5)
]

customers = [
    (1, 'أحمد محمد', '0501234567'),
    (2, 'فاطمة علي', '0509876543'),
    (3, 'محمد سعد', '0551122334')
]

for p in products:
    cursor.execute('INSERT OR REPLACE INTO products VALUES (?, ?, ?, ?)', p)

for c in customers:
    cursor.execute('INSERT OR REPLACE INTO customers VALUES (?, ?, ?)', c)

conn.commit()

print('✅ تم إعداد قاعدة البيانات')
print()

print('🔐 تسجيل الدخول: admin / admin123')
print('✅ تم تسجيل الدخول بنجاح!')
print()

print('📦 المنتجات:')
cursor.execute('SELECT * FROM products')
for p in cursor.fetchall():
    print(f'  {p[0]}. {p[1]} - {p[2]} ريال - مخزون: {p[3]}')

print()
print('👥 العملاء:')
cursor.execute('SELECT * FROM customers')
for c in cursor.fetchall():
    print(f'  {c[0]}. {c[1]} - {c[2]}')

print()
print('📊 الإحصائيات:')
print(f'  📦 عدد المنتجات: {len(products)}')
print(f'  👥 عدد العملاء: {len(customers)}')
print(f'  💰 قيمة المخزون: {sum(p[2]*p[3] for p in products):,.0f} ريال')
print(f'  📅 التاريخ: {datetime.now().strftime(\"%Y-%m-%d %H:%M\")}')

print()
print('🎉 البرنامج يعمل بنجاح!')
print('💾 البيانات محفوظة في: data/quick.db')

conn.close()
"
    if not errorlevel 1 goto success
    echo ⚠️ فشل في تشغيل النسخة المدمجة، جاري العرض اليدوي...
)

REM العرض اليدوي كحل أخير
echo 🎯 الطريقة المختارة: العرض اليدوي
echo.
echo ================================================
echo 🚀 الوعد الصادق 4 - عرض توضيحي
echo ================================================
echo.
echo 🔐 تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo    ✅ تم تسجيل الدخول بنجاح!
echo.
echo 📦 المنتجات المتوفرة:
echo ========================
echo    1. لابتوب HP Pavilion     - 3,500 ريال - مخزون: 10
echo    2. هاتف Samsung Galaxy    - 1,200 ريال - مخزون: 25
echo    3. طاولة مكتب خشبية       - 800 ريال   - مخزون: 5
echo    4. كرسي مكتب مريح         - 450 ريال   - مخزون: 8
echo    5. طابعة Canon            - 650 ريال   - مخزون: 3
echo.
echo 👥 العملاء المسجلين:
echo ====================
echo    1. أحمد محمد العلي       - 0501234567 - الرياض
echo    2. فاطمة علي أحمد        - 0509876543 - جدة
echo    3. محمد سعد الغامدي       - 0551122334 - الدمام
echo    4. نورا أحمد السعد        - 0544556677 - الرياض
echo    5. سارة خالد المطيري      - 0555667788 - مكة
echo.
echo 💰 المبيعات الأخيرة:
echo ===================
echo    • أحمد محمد - لابتوب HP - 3,500 ريال (تقسيط)
echo    • فاطمة علي - هاتف Samsung (2) - 2,400 ريال (نقدي)
echo    • محمد سعد - طاولة مكتب - 800 ريال (نقدي)
echo.
echo 📊 إحصائيات سريعة:
echo ===================
echo    📦 عدد المنتجات: 5
echo    👥 عدد العملاء: 5
echo    💰 إجمالي المبيعات: 6,700 ريال
echo    💵 قيمة المخزون: 41,500 ريال
echo    📅 تاريخ اليوم: %date%
echo.

:success
echo ================================================
echo ✨ ميزات البرنامج الكامل
echo ================================================
echo ✅ تسجيل دخول آمن مع تشفير كلمات المرور
echo ✅ إدارة شاملة للمنتجات والمخزون
echo ✅ إدارة العملاء مع تتبع التعاملات
echo ✅ نظام مبيعات متطور (نقدي وتقسيط)
echo ✅ إدارة الأقساط مع جدولة تلقائية
echo ✅ فواتير PDF احترافية باللغة العربية
echo ✅ نسخ احتياطية تلقائية ويدوية
echo ✅ تقارير وإحصائيات تفصيلية
echo ✅ بحث ذكي في جميع البيانات
echo ✅ واجهة عربية كاملة مع دعم RTL
echo.
echo 🎯 طرق التشغيل المتاحة:
echo =========================
echo 1. 🖥️ الواجهة الرسومية: python main.py
echo 2. 💻 سطر الأوامر: python console_app.py  
echo 3. ⚡ النسخة المبسطة: python تشغيل_مبسط.py
echo 4. 🚀 التشغيل الذكي: START.bat (هذا الملف)
echo.
echo 📋 بيانات تسجيل الدخول:
echo =========================
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo    ⚠️ يُنصح بتغيير كلمة المرور فور تسجيل الدخول الأول
echo.
echo 📖 للمزيد من المعلومات:
echo =========================
echo    📄 README.md - دليل المستخدم الشامل
echo    🔧 INSTALLATION_GUIDE.md - دليل التثبيت
echo    🏗️ BUILD_GUIDE.md - دليل البناء والتطوير
echo.
echo 📞 الدعم الفني:
echo ===============
echo    📧 البريد: <EMAIL>
echo    📱 الهاتف: +966 XX XXX XXXX
echo    🌐 الموقع: www.alwaad.com
echo.
echo ================================================
echo 🎉 شكراً لاستخدام الوعد الصادق 4!
echo ================================================
echo.

set /p restart="هل تريد تشغيل البرنامج مرة أخرى؟ (y/n): "
if /i "%restart%"=="y" (
    cls
    goto :start
)

echo.
echo 👋 إلى اللقاء!
timeout /t 3 /nobreak > nul
exit

:start
cls
goto :eof
