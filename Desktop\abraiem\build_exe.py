# -*- coding: utf-8 -*-
"""
سكريبت بناء ملف EXE لبرنامج الوعد الصادق 4
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

class ExeBuilder:
    def __init__(self):
        self.app_name = "الوعد الصادق 4"
        self.app_version = "4.0.0"
        self.main_script = "main.py"
        self.console_script = "console_app.py"
        self.build_dir = Path("build")
        self.dist_dir = Path("dist")
        
    def check_pyinstaller(self):
        """التحقق من وجود PyInstaller"""
        try:
            import PyInstaller
            print("✅ PyInstaller متوفر")
            return True
        except ImportError:
            print("❌ PyInstaller غير متوفر")
            print("🔧 جاري تثبيت PyInstaller...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
                print("✅ تم تثبيت PyInstaller بنجاح")
                return True
            except Exception as e:
                print(f"❌ فشل في تثبيت PyInstaller: {e}")
                return False
    
    def create_spec_file(self):
        """إنشاء ملف .spec لـ PyInstaller"""
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# تحليل الملف الرئيسي
a = Analysis(
    ['{self.main_script}'],
    pathex=[],
    binaries=[],
    datas=[
        ('config', 'config'),
        ('database', 'database'),
        ('models', 'models'),
        ('ui', 'ui'),
        ('utils', 'utils'),
        ('resources', 'resources'),
        ('README.md', '.'),
        ('requirements.txt', '.'),
        ('INSTALLATION_GUIDE.md', '.'),
        ('PROJECT_SUMMARY.md', '.'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'sqlite3',
        'bcrypt',
        'reportlab',
        'arabic_reshaper',
        'bidi',
        'openpyxl',
        'PIL',
        'dateutil',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# معالجة الملفات
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# إنشاء ملف EXE
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.app_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # واجهة رسومية
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt',
    icon='resources/icon.ico' if os.path.exists('resources/icon.ico') else None,
)

# إنشاء ملف EXE للكونسول
exe_console = EXE(
    PYZ(
        Analysis(
            ['{self.console_script}'],
            pathex=[],
            binaries=[],
            datas=[
                ('config', 'config'),
                ('database', 'database'), 
                ('models', 'models'),
                ('utils', 'utils'),
            ],
            hiddenimports=['sqlite3', 'bcrypt'],
            hookspath=[],
            hooksconfig={{}},
            runtime_hooks=[],
            excludes=[],
            win_no_prefer_redirects=False,
            win_private_assemblies=False,
            cipher=block_cipher,
            noarchive=False,
        ).pure,
        Analysis(
            ['{self.console_script}'],
            pathex=[],
            binaries=[],
            datas=[
                ('config', 'config'),
                ('database', 'database'),
                ('models', 'models'), 
                ('utils', 'utils'),
            ],
            hiddenimports=['sqlite3', 'bcrypt'],
            hookspath=[],
            hooksconfig={{}},
            runtime_hooks=[],
            excludes=[],
            win_no_prefer_redirects=False,
            win_private_assemblies=False,
            cipher=block_cipher,
            noarchive=False,
        ).zipped_data,
        cipher=block_cipher
    ),
    Analysis(
        ['{self.console_script}'],
        pathex=[],
        binaries=[],
        datas=[
            ('config', 'config'),
            ('database', 'database'),
            ('models', 'models'),
            ('utils', 'utils'),
        ],
        hiddenimports=['sqlite3', 'bcrypt'],
        hookspath=[],
        hooksconfig={{}},
        runtime_hooks=[],
        excludes=[],
        win_no_prefer_redirects=False,
        win_private_assemblies=False,
        cipher=block_cipher,
        noarchive=False,
    ).scripts,
    Analysis(
        ['{self.console_script}'],
        pathex=[],
        binaries=[],
        datas=[
            ('config', 'config'),
            ('database', 'database'),
            ('models', 'models'),
            ('utils', 'utils'),
        ],
        hiddenimports=['sqlite3', 'bcrypt'],
        hookspath=[],
        hooksconfig={{}},
        runtime_hooks=[],
        excludes=[],
        win_no_prefer_redirects=False,
        win_private_assemblies=False,
        cipher=block_cipher,
        noarchive=False,
    ).binaries,
    Analysis(
        ['{self.console_script}'],
        pathex=[],
        binaries=[],
        datas=[
            ('config', 'config'),
            ('database', 'database'),
            ('models', 'models'),
            ('utils', 'utils'),
        ],
        hiddenimports=['sqlite3', 'bcrypt'],
        hookspath=[],
        hooksconfig={{}},
        runtime_hooks=[],
        excludes=[],
        win_no_prefer_redirects=False,
        win_private_assemblies=False,
        cipher=block_cipher,
        noarchive=False,
    ).zipfiles,
    Analysis(
        ['{self.console_script}'],
        pathex=[],
        binaries=[],
        datas=[
            ('config', 'config'),
            ('database', 'database'),
            ('models', 'models'),
            ('utils', 'utils'),
        ],
        hiddenimports=['sqlite3', 'bcrypt'],
        hookspath=[],
        hooksconfig={{}},
        runtime_hooks=[],
        excludes=[],
        win_no_prefer_redirects=False,
        win_private_assemblies=False,
        cipher=block_cipher,
        noarchive=False,
    ).datas,
    [],
    name='{self.app_name} Console',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # واجهة سطر أوامر
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
        
        with open("alwaad_alsadiq.spec", "w", encoding="utf-8") as f:
            f.write(spec_content)
        
        print("✅ تم إنشاء ملف .spec")
    
    def create_version_info(self):
        """إنشاء ملف معلومات الإصدار"""
        version_info = f'''# UTF-8
#
# معلومات إصدار برنامج الوعد الصادق 4
#

VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(4, 0, 0, 0),
    prodvers=(4, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'الوعد الصادق'),
        StringStruct(u'FileDescription', u'نظام إدارة المبيعات والتقسيط'),
        StringStruct(u'FileVersion', u'{self.app_version}'),
        StringStruct(u'InternalName', u'alwaad_alsadiq'),
        StringStruct(u'LegalCopyright', u'© 2024 الوعد الصادق. جميع الحقوق محفوظة.'),
        StringStruct(u'OriginalFilename', u'{self.app_name}.exe'),
        StringStruct(u'ProductName', u'{self.app_name}'),
        StringStruct(u'ProductVersion', u'{self.app_version}')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
        
        with open("version_info.txt", "w", encoding="utf-8") as f:
            f.write(version_info)
        
        print("✅ تم إنشاء ملف معلومات الإصدار")
    
    def build_exe(self):
        """بناء ملف EXE"""
        print("🔨 جاري بناء ملف EXE...")
        
        try:
            # تنظيف المجلدات السابقة
            if self.build_dir.exists():
                shutil.rmtree(self.build_dir)
            if self.dist_dir.exists():
                shutil.rmtree(self.dist_dir)
            
            # بناء EXE
            subprocess.check_call([
                sys.executable, "-m", "PyInstaller",
                "--clean",
                "--noconfirm", 
                "alwaad_alsadiq.spec"
            ])
            
            print("✅ تم بناء ملف EXE بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ فشل في بناء EXE: {e}")
            return False
    
    def create_installer_script(self):
        """إنشاء سكريبت التثبيت"""
        installer_content = f'''@echo off
chcp 65001 > nul
title تثبيت {self.app_name}

echo ================================================
echo 🚀 مرحباً بك في برنامج تثبيت {self.app_name}
echo ================================================
echo.

echo 📋 معلومات البرنامج:
echo    الاسم: {self.app_name}
echo    الإصدار: {self.app_version}
echo    المطور: فريق الوعد الصادق
echo.

set /p install_path="📁 مجلد التثبيت (اتركه فارغاً للمجلد الافتراضي): "
if "%install_path%"=="" set install_path=C:\\Program Files\\{self.app_name}

echo.
echo 📁 سيتم تثبيت البرنامج في: %install_path%
echo.

set /p confirm="هل تريد المتابعة؟ (y/n): "
if /i not "%confirm%"=="y" (
    echo ❌ تم إلغاء التثبيت
    pause
    exit /b 1
)

echo.
echo 📦 جاري التثبيت...

REM إنشاء مجلد التثبيت
if not exist "%install_path%" mkdir "%install_path%"

REM نسخ الملفات
echo 📄 جاري نسخ الملفات...
xcopy /E /I /Y "dist\\{self.app_name}\\*" "%install_path%\\" > nul
if errorlevel 1 (
    echo ❌ فشل في نسخ الملفات
    pause
    exit /b 1
)

REM إنشاء اختصار سطح المكتب
echo 🔗 جاري إنشاء اختصار سطح المكتب...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\{self.app_name}.lnk'); $Shortcut.TargetPath = '%install_path%\\{self.app_name}.exe'; $Shortcut.WorkingDirectory = '%install_path%'; $Shortcut.Save()"

REM إنشاء اختصار قائمة ابدأ
echo 📋 جاري إنشاء اختصار قائمة ابدأ...
if not exist "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\{self.app_name}" mkdir "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\{self.app_name}"
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\{self.app_name}\\{self.app_name}.lnk'); $Shortcut.TargetPath = '%install_path%\\{self.app_name}.exe'; $Shortcut.WorkingDirectory = '%install_path%'; $Shortcut.Save()"

REM تسجيل البرنامج في النظام
echo 📝 جاري تسجيل البرنامج...
reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{self.app_name}" /v "DisplayName" /t REG_SZ /d "{self.app_name}" /f > nul 2>&1
reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{self.app_name}" /v "DisplayVersion" /t REG_SZ /d "{self.app_version}" /f > nul 2>&1
reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{self.app_name}" /v "Publisher" /t REG_SZ /d "فريق الوعد الصادق" /f > nul 2>&1
reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{self.app_name}" /v "InstallLocation" /t REG_SZ /d "%install_path%" /f > nul 2>&1
reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{self.app_name}" /v "UninstallString" /t REG_SZ /d "%install_path%\\uninstall.bat" /f > nul 2>&1

REM إنشاء ملف إلغاء التثبيت
echo 🗑️ جاري إنشاء ملف إلغاء التثبيت...
echo @echo off > "%install_path%\\uninstall.bat"
echo title إلغاء تثبيت {self.app_name} >> "%install_path%\\uninstall.bat"
echo echo جاري إلغاء تثبيت {self.app_name}... >> "%install_path%\\uninstall.bat"
echo taskkill /f /im "{self.app_name}.exe" ^>nul 2^>^&1 >> "%install_path%\\uninstall.bat"
echo timeout /t 2 /nobreak ^>nul >> "%install_path%\\uninstall.bat"
echo del "%USERPROFILE%\\Desktop\\{self.app_name}.lnk" ^>nul 2^>^&1 >> "%install_path%\\uninstall.bat"
echo rmdir /s /q "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\{self.app_name}" ^>nul 2^>^&1 >> "%install_path%\\uninstall.bat"
echo reg delete "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{self.app_name}" /f ^>nul 2^>^&1 >> "%install_path%\\uninstall.bat"
echo cd /d "%install_path%\\.." >> "%install_path%\\uninstall.bat"
echo rmdir /s /q "{self.app_name}" >> "%install_path%\\uninstall.bat"
echo echo تم إلغاء التثبيت بنجاح >> "%install_path%\\uninstall.bat"
echo pause >> "%install_path%\\uninstall.bat"

echo.
echo ✅ تم تثبيت البرنامج بنجاح!
echo.
echo 📋 تفاصيل التثبيت:
echo    📁 مجلد التثبيت: %install_path%
echo    🔗 اختصار سطح المكتب: تم إنشاؤه
echo    📋 قائمة ابدأ: تم إنشاؤها
echo.
echo 🚀 يمكنك الآن تشغيل البرنامج من:
echo    - اختصار سطح المكتب
echo    - قائمة ابدأ
echo    - الملف: %install_path%\\{self.app_name}.exe
echo.
echo 📋 بيانات تسجيل الدخول الافتراضية:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.

set /p run_now="هل تريد تشغيل البرنامج الآن؟ (y/n): "
if /i "%run_now%"=="y" (
    start "" "%install_path%\\{self.app_name}.exe"
)

echo.
echo 🎉 شكراً لاستخدام {self.app_name}!
pause
'''
        
        with open("installer.bat", "w", encoding="utf-8") as f:
            f.write(installer_content)
        
        print("✅ تم إنشاء سكريبت التثبيت")
    
    def build(self):
        """بناء التطبيق الكامل"""
        print("🏗️ بدء بناء التطبيق الاحترافي...")
        print("=" * 50)
        
        # التحقق من PyInstaller
        if not self.check_pyinstaller():
            return False
        
        # إنشاء الملفات المطلوبة
        self.create_spec_file()
        self.create_version_info()
        
        # بناء EXE
        if not self.build_exe():
            return False
        
        # إنشاء سكريبت التثبيت
        self.create_installer_script()
        
        print("=" * 50)
        print("🎉 تم بناء التطبيق بنجاح!")
        print("📁 الملفات الناتجة:")
        print(f"   📦 {self.app_name}.exe - التطبيق الرئيسي")
        print(f"   📦 {self.app_name} Console.exe - نسخة سطر الأوامر")
        print("   🔧 installer.bat - ملف التثبيت")
        print("=" * 50)
        
        return True

def main():
    """تشغيل بناء التطبيق"""
    builder = ExeBuilder()
    
    try:
        success = builder.build()
        if success:
            print("✅ تم بناء التطبيق بنجاح!")
            print("🚀 يمكنك الآن توزيع البرنامج كتطبيق احترافي")
        else:
            print("❌ فشل في بناء التطبيق")
            
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البناء بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
