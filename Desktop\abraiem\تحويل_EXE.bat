@echo off
chcp 65001 > nul
title تحويل الوعد الصادق 4 إلى EXE

color 0E
echo.
echo ████████████████████████████████████████████████████████
echo █                                                      █
echo █        🔧 تحويل الوعد الصادق 4 إلى EXE 🔧           █
echo █                                                      █
echo ████████████████████████████████████████████████████████
echo.
echo 📋 سيتم تحويل البرنامج إلى ملفات EXE قابلة للتشغيل
echo 💻 بدون الحاجة لتثبيت Python على الأجهزة الأخرى
echo.

echo ================================================
echo 🔍 جاري فحص النظام...
echo ================================================
echo.

REM التحقق من Python
echo [1/4] فحص Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير متوفر
    echo 📥 يرجى تثبيت Python من: https://www.python.org/downloads/
    echo ⚠️ تأكد من تفعيل خيار "Add Python to PATH"
    pause
    exit /b 1
)
echo ✅ Python متوفر

REM التحقق من pip
echo [2/4] فحص pip...
python -m pip --version > nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    echo 🔧 جاري تثبيت pip...
    python -m ensurepip --upgrade
)
echo ✅ pip متوفر

REM تثبيت PyInstaller
echo [3/4] تثبيت PyInstaller...
python -c "import PyInstaller" > nul 2>&1
if errorlevel 1 (
    echo 📦 جاري تثبيت PyInstaller...
    python -m pip install pyinstaller
    if errorlevel 1 (
        echo ❌ فشل في تثبيت PyInstaller
        pause
        exit /b 1
    )
)
echo ✅ PyInstaller متوفر

REM فحص الملفات
echo [4/4] فحص ملفات البرنامج...
if not exist "main.py" (
    echo ❌ main.py غير موجود
    pause
    exit /b 1
)
if not exist "تشغيل_مبسط.py" (
    echo ❌ تشغيل_مبسط.py غير موجود
    pause
    exit /b 1
)
echo ✅ جميع الملفات موجودة

echo.
echo ================================================
echo 🔨 بدء عملية التحويل...
echo ================================================
echo.

REM إنشاء مجلدات الإخراج
if not exist "EXE_Output" mkdir EXE_Output

echo 🎯 الطريقة 1: تحويل تلقائي شامل
echo ================================
python تحويل_الى_exe.py
if not errorlevel 1 (
    echo ✅ تم التحويل التلقائي بنجاح
    goto organize_files
)

echo ⚠️ فشل التحويل التلقائي، جاري المحاولة اليدوية...
echo.

echo 🎯 الطريقة 2: تحويل يدوي مبسط
echo ===============================

REM تحويل النسخة المبسطة
echo 📦 جاري تحويل النسخة المبسطة...
pyinstaller --onefile --console --name "الوعد الصادق 4 - مبسط" --distpath EXE_Output تشغيل_مبسط.py
if not errorlevel 1 (
    echo ✅ تم تحويل النسخة المبسطة
) else (
    echo ⚠️ فشل في تحويل النسخة المبسطة
)

REM تحويل نسخة سطر الأوامر
echo 📦 جاري تحويل نسخة سطر الأوامر...
pyinstaller --onefile --console --name "الوعد الصادق 4 - كونسول" --distpath EXE_Output console_app.py
if not errorlevel 1 (
    echo ✅ تم تحويل نسخة سطر الأوامر
) else (
    echo ⚠️ فشل في تحويل نسخة سطر الأوامر
)

REM تحويل الواجهة الرسومية (إذا كانت متوفرة)
python -c "import PyQt5" > nul 2>&1
if not errorlevel 1 (
    echo 📦 جاري تحويل الواجهة الرسومية...
    pyinstaller --onefile --windowed --name "الوعد الصادق 4" --distpath EXE_Output main.py
    if not errorlevel 1 (
        echo ✅ تم تحويل الواجهة الرسومية
    ) else (
        echo ⚠️ فشل في تحويل الواجهة الرسومية
    )
) else (
    echo ⚠️ PyQt5 غير متوفر، تم تخطي الواجهة الرسومية
)

:organize_files
echo.
echo ================================================
echo 📁 تنظيم الملفات...
echo ================================================

REM نسخ ملفات EXE إلى مجلد واحد
if exist "dist" (
    echo 📄 نسخ ملفات من مجلد dist...
    for /r dist %%f in (*.exe) do (
        copy "%%f" EXE_Output\ > nul 2>&1
        echo ✅ نسخ %%~nxf
    )
)

if exist "EXE_Files" (
    echo 📄 نسخ ملفات من مجلد EXE_Files...
    copy EXE_Files\*.exe EXE_Output\ > nul 2>&1
)

REM إنشاء ملف تعليمات
echo 📝 إنشاء ملف التعليمات...
(
echo الوعد الصادق 4 - ملفات EXE
echo ============================
echo.
echo 📋 طريقة الاستخدام:
echo ==================
echo 1. انقر مرتين على أي ملف EXE
echo 2. سيعمل البرنامج فوراً بدون الحاجة لـ Python
echo 3. يمكن نسخ الملفات إلى أي جهاز آخر
echo.
echo 🔐 بيانات تسجيل الدخول:
echo ========================
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo 📦 الملفات المتوفرة:
echo ===================
) > EXE_Output\تعليمات_الاستخدام.txt

REM إضافة قائمة الملفات إلى التعليمات
for %%f in (EXE_Output\*.exe) do (
    echo - %%~nxf >> EXE_Output\تعليمات_الاستخدام.txt
)

(
echo.
echo 💡 ملاحظات مهمة:
echo ================
echo - جميع الملفات تعمل بشكل مستقل
echo - لا تحتاج تثبيت أي برامج إضافية
echo - البيانات محفوظة في مجلد data/
echo - يمكن توزيع الملفات تجارياً
echo.
echo تاريخ الإنشاء: %date% %time%
echo المطور: فريق الوعد الصادق
echo الإصدار: 4.0.0
) >> EXE_Output\تعليمات_الاستخدام.txt

REM تنظيف الملفات المؤقتة
echo 🧹 تنظيف الملفات المؤقتة...
if exist "build" rmdir /s /q build > nul 2>&1
if exist "dist" rmdir /s /q dist > nul 2>&1
if exist "*.spec" del *.spec > nul 2>&1

echo.
echo ================================================
echo 📊 إحصائيات التحويل
echo ================================================

REM حساب عدد ملفات EXE
set exe_count=0
for %%f in (EXE_Output\*.exe) do (
    set /a exe_count+=1
)

echo 📦 عدد ملفات EXE المنشأة: %exe_count%
echo 📁 مجلد الإخراج: EXE_Output\

if %exe_count% GTR 0 (
    echo.
    echo 📋 الملفات المنشأة:
    echo ==================
    for %%f in (EXE_Output\*.exe) do (
        echo ✅ %%~nxf
        for %%s in ("%%f") do echo    الحجم: %%~zs بايت
    )
) else (
    echo ❌ لم يتم إنشاء أي ملفات EXE
    echo 💡 تأكد من تثبيت جميع المتطلبات وأعد المحاولة
)

echo.
echo ================================================
echo 🎉 انتهت عملية التحويل!
echo ================================================
echo.

if %exe_count% GTR 0 (
    echo ✅ تم تحويل البرنامج إلى EXE بنجاح!
    echo.
    echo 🚀 يمكنك الآن:
    echo ===============
    echo 1. تشغيل أي ملف EXE مباشرة
    echo 2. نسخ مجلد EXE_Output إلى أي جهاز
    echo 3. توزيع البرنامج بدون Python
    echo 4. بيع البرنامج كمنتج تجاري
    echo.
    echo 📋 بيانات تسجيل الدخول:
    echo ========================
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    echo.
    
    set /p open_folder="هل تريد فتح مجلد الملفات؟ (y/n): "
    if /i "!open_folder!"=="y" (
        explorer EXE_Output
    )
    
    set /p test_exe="هل تريد تجربة أحد الملفات؟ (y/n): "
    if /i "!test_exe!"=="y" (
        echo 🚀 جاري تشغيل أول ملف EXE...
        for %%f in (EXE_Output\*.exe) do (
            start "" "%%f"
            goto end_test
        )
        :end_test
    )
) else (
    echo ❌ فشل في التحويل
    echo.
    echo 🔧 حلول مقترحة:
    echo ================
    echo 1. تأكد من تثبيت Python بشكل صحيح
    echo 2. تأكد من إضافة Python إلى PATH
    echo 3. شغل Command Prompt كمدير
    echo 4. تأكد من اتصال الإنترنت لتثبيت المكتبات
)

echo.
echo 🎊 شكراً لاستخدام الوعد الصادق 4!
pause
