; ملف إعداد Inno Setup لبرنامج الوعد الصادق 4
; يتطلب Inno Setup 6.0 أو أحدث

[Setup]
; معلومات التطبيق الأساسية
AppId={{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
AppName=الوعد الصادق 4
AppVersion=4.0.0
AppVerName=الوعد الصادق 4 الإصدار 4.0.0
AppPublisher=فريق الوعد الصادق
AppPublisherURL=https://www.alwaad.com
AppSupportURL=https://www.alwaad.com/support
AppUpdatesURL=https://www.alwaad.com/updates
AppCopyright=© 2024 الوعد الصادق. جميع الحقوق محفوظة.

; مجلد التثبيت الافتراضي
DefaultDirName={autopf}\الوعد الصادق 4
DefaultGroupName=الوعد الصادق 4
DisableProgramGroupPage=yes

; ملف الترخيص والمعلومات
LicenseFile=LICENSE.txt
InfoBeforeFile=README.md
InfoAfterFile=INSTALLATION_GUIDE.md

; إعدادات الإخراج
OutputDir=setup_output
OutputBaseFilename=الوعد_الصادق_4_Setup
SetupIconFile=resources\icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern

; متطلبات النظام
MinVersion=6.1sp1
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

; إعدادات اللغة والواجهة
ShowLanguageDialog=no
LanguageDetectionMethod=locale

; إعدادات إضافية
PrivilegesRequired=admin
DisableWelcomePage=no
DisableReadyPage=no
DisableFinishedPage=no
AllowNoIcons=yes
UninstallDisplayIcon={app}\الوعد الصادق 4.exe
UninstallDisplayName=الوعد الصادق 4

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1
Name: "associatefiles"; Description: "ربط ملفات البيانات بالبرنامج"; GroupDescription: "إعدادات إضافية"
Name: "startmenu"; Description: "إضافة إلى قائمة ابدأ"; GroupDescription: "إعدادات إضافية"; Flags: checked

[Files]
; الملف الرئيسي
Source: "dist\الوعد الصادق 4\الوعد الصادق 4.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "dist\الوعد الصادق 4\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

; ملفات التوثيق
Source: "README.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "INSTALLATION_GUIDE.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "PROJECT_SUMMARY.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "requirements.txt"; DestDir: "{app}"; Flags: ignoreversion

; ملفات الموارد
Source: "resources\*"; DestDir: "{app}\resources"; Flags: ignoreversion recursesubdirs createallsubdirs

; مجلدات البيانات (فارغة)
Source: "data\.gitkeep"; DestDir: "{app}\data"; Flags: ignoreversion
Source: "backups\.gitkeep"; DestDir: "{app}\backups"; Flags: ignoreversion  
Source: "reports\.gitkeep"; DestDir: "{app}\reports"; Flags: ignoreversion

[Icons]
; اختصار قائمة ابدأ
Name: "{group}\الوعد الصادق 4"; Filename: "{app}\الوعد الصادق 4.exe"; WorkingDir: "{app}"; IconFilename: "{app}\resources\icon.ico"
Name: "{group}\دليل المستخدم"; Filename: "{app}\README.md"
Name: "{group}\إلغاء التثبيت"; Filename: "{uninstallexe}"

; اختصار سطح المكتب
Name: "{autodesktop}\الوعد الصادق 4"; Filename: "{app}\الوعد الصادق 4.exe"; WorkingDir: "{app}"; IconFilename: "{app}\resources\icon.ico"; Tasks: desktopicon

; اختصار التشغيل السريع
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\الوعد الصادق 4"; Filename: "{app}\الوعد الصادق 4.exe"; WorkingDir: "{app}"; Tasks: quicklaunchicon

[Registry]
; تسجيل البرنامج في النظام
Root: HKLM; Subkey: "Software\الوعد الصادق\الوعد الصادق 4"; ValueType: string; ValueName: "InstallPath"; ValueData: "{app}"
Root: HKLM; Subkey: "Software\الوعد الصادق\الوعد الصادق 4"; ValueType: string; ValueName: "Version"; ValueData: "4.0.0"
Root: HKLM; Subkey: "Software\الوعد.صادق\الوعد الصادق 4"; ValueType: dword; ValueName: "Installed"; ValueData: 1

; ربط أنواع الملفات
Root: HKCR; Subkey: ".alwaad"; ValueType: string; ValueName: ""; ValueData: "AlwaadFile"; Flags: uninsdeletevalue; Tasks: associatefiles
Root: HKCR; Subkey: "AlwaadFile"; ValueType: string; ValueName: ""; ValueData: "ملف بيانات الوعد الصادق"; Flags: uninsdeletekey; Tasks: associatefiles
Root: HKCR; Subkey: "AlwaadFile\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\resources\icon.ico"; Tasks: associatefiles
Root: HKCR; Subkey: "AlwaadFile\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\الوعد الصادق 4.exe"" ""%1"""; Tasks: associatefiles

[Run]
; تشغيل البرنامج بعد التثبيت
Filename: "{app}\الوعد الصادق 4.exe"; Description: "{cm:LaunchProgram,الوعد الصادق 4}"; Flags: nowait postinstall skipifsilent

[UninstallRun]
; تنظيف قبل إلغاء التثبيت
Filename: "{app}\الوعد الصادق 4.exe"; Parameters: "--cleanup"; Flags: skipifdoesntexist

[UninstallDelete]
; حذف ملفات إضافية عند إلغاء التثبيت
Type: filesandordirs; Name: "{app}\data"
Type: filesandordirs; Name: "{app}\logs"
Type: files; Name: "{app}\*.log"
Type: files; Name: "{app}\*.tmp"

[Code]
// كود Pascal للتحكم في عملية التثبيت

function GetUninstallString(): String;
var
  sUnInstPath: String;
  sUnInstallString: String;
begin
  sUnInstPath := ExpandConstant('Software\Microsoft\Windows\CurrentVersion\Uninstall\{#SetupSetting("AppId")}_is1');
  sUnInstallString := '';
  if not RegQueryStringValue(HKLM, sUnInstPath, 'UninstallString', sUnInstallString) then
    RegQueryStringValue(HKCU, sUnInstPath, 'UninstallString', sUnInstallString);
  Result := sUnInstallString;
end;

function IsUpgrade(): Boolean;
begin
  Result := (GetUninstallString() <> '');
end;

function UnInstallOldVersion(): Integer;
var
  sUnInstallString: String;
  iResultCode: Integer;
begin
  Result := 0;
  sUnInstallString := GetUninstallString();
  if sUnInstallString <> '' then begin
    sUnInstallString := RemoveQuotes(sUnInstallString);
    if Exec(sUnInstallString, '/SILENT /NORESTART /SUPPRESSMSGBOXES','', SW_HIDE, ewWaitUntilTerminated, iResultCode) then
      Result := 3
    else
      Result := 2;
  end else
    Result := 1;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if (CurStep=ssInstall) then
  begin
    if (IsUpgrade()) then
    begin
      UnInstallOldVersion();
    end;
  end;
end;

function InitializeSetup(): Boolean;
var
  Version: TWindowsVersion;
begin
  GetWindowsVersionEx(Version);
  
  // التحقق من إصدار Windows
  if Version.Major < 6 then begin
    MsgBox('هذا البرنامج يتطلب Windows 7 أو أحدث.', mbError, MB_OK);
    Result := False;
    Exit;
  end;
  
  // التحقق من المساحة المتوفرة
  if GetSpaceOnDisk(ExpandConstant('{app}'), False, False, False) < 100*1024*1024 then begin
    MsgBox('المساحة المتوفرة غير كافية. يتطلب البرنامج 100 ميجابايت على الأقل.', mbError, MB_OK);
    Result := False;
    Exit;
  end;
  
  Result := True;
end;

procedure InitializeWizard();
begin
  // تخصيص واجهة التثبيت
  WizardForm.Caption := 'تثبيت الوعد الصادق 4';
  WizardForm.WelcomeLabel1.Caption := 'مرحباً بك في معالج تثبيت الوعد الصادق 4';
  WizardForm.WelcomeLabel2.Caption := 'سيقوم هذا المعالج بتثبيت الوعد الصادق 4 على جهازك.' + #13#13 + 
    'يُنصح بإغلاق جميع التطبيقات الأخرى قبل المتابعة.' + #13#13 +
    'اضغط التالي للمتابعة.';
end;

function NextButtonClick(CurPageID: Integer): Boolean;
begin
  Result := True;
  
  // التحقق من الصفحات المختلفة
  case CurPageID of
    wpSelectDir:
    begin
      // التحقق من صحة مجلد التثبيت
      if not DirExists(WizardForm.DirEdit.Text) then begin
        if not CreateDir(WizardForm.DirEdit.Text) then begin
          MsgBox('لا يمكن إنشاء مجلد التثبيت المحدد.', mbError, MB_OK);
          Result := False;
        end;
      end;
    end;
  end;
end;

procedure CurPageChanged(CurPageID: Integer);
begin
  case CurPageID of
    wpFinished:
    begin
      // رسالة نهاية التثبيت
      WizardForm.FinishedLabel.Caption := 'تم تثبيت الوعد الصادق 4 بنجاح!' + #13#13 +
        'بيانات تسجيل الدخول الافتراضية:' + #13 +
        'اسم المستخدم: admin' + #13 +
        'كلمة المرور: admin123' + #13#13 +
        'يُنصح بتغيير كلمة المرور فور تسجيل الدخول الأول.';
    end;
  end;
end;
