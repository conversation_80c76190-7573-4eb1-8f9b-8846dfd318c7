# -*- coding: utf-8 -*-
"""
الملف الرئيسي لبرنامج الوعد الصادق 4
نظام إدارة المبيعات والتقسيط
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor
from config.settings import create_directories, UI_CONFIG, COMPANY_INFO
from ui.login_window import LoginWindow
from ui.main_window import MainWindow

class AlwaadAlsadiqApp:
    def __init__(self):
        self.app = None
        self.login_window = None
        self.main_window = None
        self.current_user = None
        
    def run(self):
        """تشغيل البرنامج"""
        try:
            # إنشاء تطبيق Qt
            self.app = QApplication(sys.argv)
            
            # إعداد التطبيق
            self.setup_application()
            
            # إنشاء المجلدات المطلوبة
            create_directories()
            
            # عرض شاشة البداية
            self.show_splash_screen()
            
            # بدء التطبيق
            sys.exit(self.app.exec_())
            
        except Exception as e:
            self.show_error(f"خطأ في تشغيل البرنامج:\n{str(e)}")
            
    def setup_application(self):
        """إعداد التطبيق"""
        # تعيين معلومات التطبيق
        self.app.setApplicationName(COMPANY_INFO['name'])
        self.app.setApplicationVersion("4.0")
        self.app.setOrganizationName("الوعد الصادق")
        
        # تعيين الخط العربي
        font = QFont(UI_CONFIG['font_family'], UI_CONFIG['font_size'])
        self.app.setFont(font)
        
        # تعيين اتجاه النص من اليمين لليسار
        if UI_CONFIG['rtl_support']:
            self.app.setLayoutDirection(Qt.RightToLeft)
            
        # تعيين أيقونة التطبيق (إذا كانت متوفرة)
        # self.app.setWindowIcon(QIcon("resources/icon.ico"))
        
    def show_splash_screen(self):
        """عرض شاشة البداية"""
        # إنشاء صورة شاشة البداية
        splash_pixmap = self.create_splash_pixmap()
        
        # إنشاء شاشة البداية
        splash = QSplashScreen(splash_pixmap)
        splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.SplashScreen)
        splash.show()
        
        # رسائل التحميل
        splash.showMessage("جاري تحميل البرنامج...", Qt.AlignBottom | Qt.AlignCenter, QColor(255, 255, 255))
        self.app.processEvents()
        
        # محاكاة تحميل البرنامج
        QTimer.singleShot(1000, lambda: splash.showMessage("جاري تحميل قاعدة البيانات...", Qt.AlignBottom | Qt.AlignCenter, QColor(255, 255, 255)))
        QTimer.singleShot(2000, lambda: splash.showMessage("جاري تحميل الواجهات...", Qt.AlignBottom | Qt.AlignCenter, QColor(255, 255, 255)))
        QTimer.singleShot(3000, lambda: self.finish_loading(splash))
        
    def create_splash_pixmap(self):
        """إنشاء صورة شاشة البداية"""
        pixmap = QPixmap(400, 300)
        pixmap.fill(QColor(44, 62, 80))  # لون خلفية
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # رسم النص
        painter.setPen(QColor(255, 255, 255))
        painter.setFont(QFont("Arial", 24, QFont.Bold))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, COMPANY_INFO['name'])
        
        # رسم النسخة
        painter.setFont(QFont("Arial", 12))
        painter.drawText(pixmap.rect().adjusted(0, 50, 0, 0), Qt.AlignCenter, "الإصدار 4.0")
        
        painter.end()
        return pixmap
        
    def finish_loading(self, splash):
        """إنهاء التحميل وعرض نافذة تسجيل الدخول"""
        splash.close()
        self.show_login()
        
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        try:
            self.login_window = LoginWindow()
            self.login_window.login_successful.connect(self.handle_login_success)
            self.login_window.show()
            
        except Exception as e:
            self.show_error(f"خطأ في عرض نافذة تسجيل الدخول:\n{str(e)}")
            
    def handle_login_success(self, user_data):
        """معالجة نجاح تسجيل الدخول"""
        try:
            self.current_user = user_data
            self.show_main_window()
            
        except Exception as e:
            self.show_error(f"خطأ في تسجيل الدخول:\n{str(e)}")
            
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        try:
            self.main_window = MainWindow(self.current_user)
            self.main_window.logout_requested.connect(self.handle_logout)
            self.main_window.show()
            
            # إخفاء نافذة تسجيل الدخول
            if self.login_window:
                self.login_window.hide()
                
        except Exception as e:
            self.show_error(f"خطأ في عرض النافذة الرئيسية:\n{str(e)}")
            
    def handle_logout(self):
        """معالجة تسجيل الخروج"""
        try:
            # إغلاق النافذة الرئيسية
            if self.main_window:
                self.main_window.close()
                self.main_window = None
                
            # مسح بيانات المستخدم الحالي
            self.current_user = None
            
            # عرض نافذة تسجيل الدخول مرة أخرى
            self.show_login()
            
        except Exception as e:
            self.show_error(f"خطأ في تسجيل الخروج:\n{str(e)}")
            
    def show_error(self, message):
        """عرض رسالة خطأ"""
        if self.app:
            QMessageBox.critical(None, "خطأ", message)
        else:
            print(f"خطأ: {message}")

def main():
    """النقطة الرئيسية لتشغيل البرنامج"""
    try:
        # التحقق من إصدار Python
        if sys.version_info < (3, 6):
            print("يتطلب البرنامج Python 3.6 أو أحدث")
            return
            
        # إنشاء وتشغيل التطبيق
        app = AlwaadAlsadiqApp()
        app.run()
        
    except KeyboardInterrupt:
        print("\nتم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"خطأ غير متوقع: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
