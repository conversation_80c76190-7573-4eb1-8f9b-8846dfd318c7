# -*- coding: utf-8 -*-
"""
نموذج المبيعات لبرنامج الوعد الصادق 4
"""

from datetime import datetime, timedelta
from database.database_manager import db_manager

class SalesModel:
    def __init__(self):
        self.db = db_manager
    
    def create_sale(self, customer_id, user_id, sale_type, items, discount_amount=0, 
                   down_payment=0, installment_count=0, interest_rate=0, notes=None):
        """إنشاء عملية بيع جديدة"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            # حساب المبلغ الإجمالي
            total_amount = sum(item['quantity'] * item['unit_price'] for item in items)
            final_amount = total_amount - discount_amount
            
            # حساب المبلغ المتبقي والقسط الشهري
            remaining_amount = final_amount - down_payment
            monthly_payment = 0
            
            if sale_type == 'installment' and installment_count > 0:
                # حساب القسط الشهري مع الفائدة
                if interest_rate > 0:
                    monthly_interest = interest_rate / 100 / 12
                    monthly_payment = remaining_amount * (monthly_interest * (1 + monthly_interest) ** installment_count) / ((1 + monthly_interest) ** installment_count - 1)
                else:
                    monthly_payment = remaining_amount / installment_count
            
            # إنشاء عملية البيع
            cursor.execute('''
                INSERT INTO sales (customer_id, user_id, sale_type, total_amount, 
                                 discount_amount, final_amount, down_payment, 
                                 remaining_amount, installment_count, interest_rate, 
                                 monthly_payment, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (customer_id, user_id, sale_type, total_amount, discount_amount, 
                  final_amount, down_payment, remaining_amount, installment_count, 
                  interest_rate, monthly_payment, notes))
            
            sale_id = cursor.lastrowid
            
            # إضافة عناصر البيع
            for item in items:
                cursor.execute('''
                    INSERT INTO sale_items (sale_id, product_id, quantity, unit_price, total_price)
                    VALUES (?, ?, ?, ?, ?)
                ''', (sale_id, item['product_id'], item['quantity'], 
                      item['unit_price'], item['quantity'] * item['unit_price']))
                
                # تحديث المخزون
                cursor.execute('''
                    UPDATE products 
                    SET stock_quantity = stock_quantity - ?, updated_at = CURRENT_TIMESTAMP 
                    WHERE id = ?
                ''', (item['quantity'], item['product_id']))
            
            # إنشاء الأقساط إذا كان البيع بالتقسيط
            if sale_type == 'installment' and installment_count > 0:
                self._create_installments(cursor, sale_id, installment_count, monthly_payment)
            
            conn.commit()
            conn.close()
            return sale_id
            
        except Exception as e:
            conn.rollback()
            conn.close()
            raise Exception(f"فشل في إنشاء عملية البيع: {str(e)}")
    
    def _create_installments(self, cursor, sale_id, installment_count, monthly_payment):
        """إنشاء جدول الأقساط"""
        current_date = datetime.now()
        
        for i in range(1, installment_count + 1):
            due_date = current_date + timedelta(days=30 * i)  # كل 30 يوم
            
            cursor.execute('''
                INSERT INTO installments (sale_id, installment_number, due_date, amount)
                VALUES (?, ?, ?, ?)
            ''', (sale_id, i, due_date.date(), monthly_payment))
    
    def get_sale_by_id(self, sale_id):
        """الحصول على عملية بيع بواسطة المعرف"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT s.*, c.name as customer_name, u.full_name as user_name
            FROM sales s
            LEFT JOIN customers c ON s.customer_id = c.id
            JOIN users u ON s.user_id = u.id
            WHERE s.id = ?
        ''', (sale_id,))
        
        sale = cursor.fetchone()
        
        if sale:
            # الحصول على عناصر البيع
            cursor.execute('''
                SELECT si.*, p.name as product_name
                FROM sale_items si
                JOIN products p ON si.product_id = p.id
                WHERE si.sale_id = ?
            ''', (sale_id,))
            
            items = cursor.fetchall()
            sale = dict(sale)
            sale['items'] = items
        
        conn.close()
        return sale
    
    def get_all_sales(self, start_date=None, end_date=None, customer_id=None, user_id=None):
        """الحصول على جميع المبيعات مع فلترة"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        query = '''
            SELECT s.id, s.sale_type, s.final_amount, s.status, s.sale_date,
                   c.name as customer_name, u.full_name as user_name
            FROM sales s
            LEFT JOIN customers c ON s.customer_id = c.id
            JOIN users u ON s.user_id = u.id
            WHERE 1=1
        '''
        params = []
        
        if start_date:
            query += " AND DATE(s.sale_date) >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND DATE(s.sale_date) <= ?"
            params.append(end_date)
        
        if customer_id:
            query += " AND s.customer_id = ?"
            params.append(customer_id)
        
        if user_id:
            query += " AND s.user_id = ?"
            params.append(user_id)
        
        query += " ORDER BY s.sale_date DESC"
        
        cursor.execute(query, params)
        sales = cursor.fetchall()
        conn.close()
        return sales
    
    def get_installments_by_sale(self, sale_id):
        """الحصول على أقساط عملية بيع معينة"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, installment_number, due_date, amount, paid_amount, 
                   payment_date, status, notes
            FROM installments
            WHERE sale_id = ?
            ORDER BY installment_number
        ''', (sale_id,))
        
        installments = cursor.fetchall()
        conn.close()
        return installments
    
    def pay_installment(self, installment_id, amount, payment_method='cash', 
                       received_by=None, notes=None):
        """دفع قسط"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            # الحصول على بيانات القسط
            cursor.execute('''
                SELECT id, sale_id, amount, paid_amount, status
                FROM installments
                WHERE id = ?
            ''', (installment_id,))
            
            installment = cursor.fetchone()
            if not installment:
                raise Exception("القسط غير موجود")
            
            new_paid_amount = installment['paid_amount'] + amount
            new_status = 'paid' if new_paid_amount >= installment['amount'] else 'pending'
            
            # تحديث القسط
            cursor.execute('''
                UPDATE installments 
                SET paid_amount = ?, status = ?, payment_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (new_paid_amount, new_status, installment_id))
            
            # إضافة سجل الدفع
            cursor.execute('''
                INSERT INTO payments (installment_id, sale_id, amount, payment_method, 
                                    received_by, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (installment_id, installment['sale_id'], amount, payment_method, 
                  received_by, notes))
            
            # تحديث المبلغ المتبقي في عملية البيع
            cursor.execute('''
                UPDATE sales 
                SET remaining_amount = remaining_amount - ?
                WHERE id = ?
            ''', (amount, installment['sale_id']))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            conn.rollback()
            conn.close()
            raise Exception(f"فشل في دفع القسط: {str(e)}")
    
    def get_overdue_installments(self):
        """الحصول على الأقساط المتأخرة"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT i.id, i.installment_number, i.due_date, i.amount, i.paid_amount,
                   s.id as sale_id, c.name as customer_name, c.phone as customer_phone
            FROM installments i
            JOIN sales s ON i.sale_id = s.id
            JOIN customers c ON s.customer_id = c.id
            WHERE i.status = 'pending' AND i.due_date < DATE('now')
            ORDER BY i.due_date
        ''')
        
        # تحديث حالة الأقساط المتأخرة
        cursor.execute('''
            UPDATE installments 
            SET status = 'overdue' 
            WHERE status = 'pending' AND due_date < DATE('now')
        ''')
        
        installments = cursor.fetchall()
        conn.commit()
        conn.close()
        return installments
