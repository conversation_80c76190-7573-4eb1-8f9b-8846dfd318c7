import sqlite3
import os
from datetime import datetime

print("🚀 برنامج الوعد الصادق 4 - عرض توضيحي")
print("=" * 60)

# إنشاء قاعدة بيانات بسيطة
os.makedirs("data", exist_ok=True)
conn = sqlite3.connect("data/demo.db")
cursor = conn.cursor()

# إنشاء الجداول
print("📊 جاري إعداد قاعدة البيانات...")

cursor.execute('''
    CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY,
        username TEXT,
        password TEXT,
        name TEXT
    )
''')

cursor.execute('''
    CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY,
        name TEXT,
        price REAL,
        stock INTEGER
    )
''')

cursor.execute('''
    CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY,
        name TEXT,
        phone TEXT
    )
''')

# إضافة بيانات تجريبية
print("📝 جاري إضافة البيانات التجريبية...")

# مستخدم
cursor.execute("INSERT OR REPLACE INTO users VALUES (1, 'admin', 'admin123', 'المدير العام')")

# منتجات
products = [
    (1, 'لابتوب HP', 3500.0, 10),
    (2, 'هاتف Samsung', 1200.0, 25),
    (3, 'طاولة مكتب', 800.0, 5)
]

for product in products:
    cursor.execute("INSERT OR REPLACE INTO products VALUES (?, ?, ?, ?)", product)

# عملاء
customers = [
    (1, 'أحمد محمد', '0501234567'),
    (2, 'فاطمة علي', '0509876543'),
    (3, 'محمد سعد', '0551122334')
]

for customer in customers:
    cursor.execute("INSERT OR REPLACE INTO customers VALUES (?, ?, ?)", customer)

conn.commit()

print("✅ تم إعداد قاعدة البيانات بنجاح!")

# عرض البيانات
print("\n" + "="*60)
print("🔐 تسجيل الدخول")
print("="*60)
print("اسم المستخدم: admin")
print("كلمة المرور: admin123")
print("✅ تم تسجيل الدخول بنجاح!")

print("\n" + "="*60)
print("📦 قائمة المنتجات")
print("="*60)
cursor.execute("SELECT * FROM products")
products = cursor.fetchall()

print(f"{'الرقم':<5} {'الاسم':<20} {'السعر':<10} {'المخزون':<10}")
print("-"*50)
for product in products:
    print(f"{product[0]:<5} {product[1]:<20} {product[2]:<10.2f} {product[3]:<10}")

print(f"\nإجمالي المنتجات: {len(products)}")

print("\n" + "="*60)
print("👥 قائمة العملاء")
print("="*60)
cursor.execute("SELECT * FROM customers")
customers = cursor.fetchall()

print(f"{'الرقم':<5} {'الاسم':<20} {'الهاتف':<15}")
print("-"*45)
for customer in customers:
    print(f"{customer[0]:<5} {customer[1]:<20} {customer[2]:<15}")

print(f"\nإجمالي العملاء: {len(customers)}")

print("\n" + "="*60)
print("📊 إحصائيات البرنامج")
print("="*60)
print(f"📦 عدد المنتجات: {len(products)}")
print(f"👥 عدد العملاء: {len(customers)}")
print(f"💾 حجم قاعدة البيانات: {os.path.getsize('data/demo.db')} بايت")
print(f"📅 تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

print("\n" + "="*60)
print("✨ ميزات البرنامج")
print("="*60)
print("✅ تسجيل دخول آمن")
print("✅ إدارة المنتجات والمخزون")
print("✅ إدارة العملاء")
print("✅ نظام المبيعات (نقدي وتقسيط)")
print("✅ إدارة الأقساط")
print("✅ فواتير PDF")
print("✅ نسخ احتياطية")
print("✅ تقارير وإحصائيات")
print("✅ دعم اللغة العربية")

print("\n" + "="*60)
print("🎉 تم تشغيل البرنامج بنجاح!")
print("="*60)
print("🚀 البرنامج الكامل جاهز للاستخدام")
print("📖 راجع ملف README.md للمزيد من التفاصيل")

conn.close()
print("💾 تم حفظ البيانات وإغلاق قاعدة البيانات")
