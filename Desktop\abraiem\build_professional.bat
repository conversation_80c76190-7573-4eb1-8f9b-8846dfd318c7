@echo off
chcp 65001 > nul
title بناء الوعد الصادق 4 - تطبيق احترافي

echo ================================================
echo 🏗️ بناء الوعد الصادق 4 كتطبيق احترافي
echo ================================================
echo.

echo 📋 معلومات البناء:
echo    الاسم: الوعد الصادق 4
echo    الإصدار: 4.0.0
echo    النوع: تطبيق Windows احترافي
echo    التاريخ: %date% %time%
echo.

echo 🔍 جاري التحقق من المتطلبات...

REM التحقق من Python
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير متوفر
    echo 📥 يرجى تثبيت Python من: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python متوفر

REM التحقق من pip
python -m pip --version > nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    echo 🔧 جاري تثبيت pip...
    python -m ensurepip --upgrade
)
echo ✅ pip متوفر

echo.
echo 📦 جاري تثبيت المتطلبات...

REM تثبيت المتطلبات الأساسية
echo 🔹 تثبيت PyInstaller...
python -m pip install pyinstaller --quiet
if errorlevel 1 (
    echo ⚠️ فشل في تثبيت PyInstaller
)

echo 🔹 تثبيت Pillow لإنشاء الأيقونات...
python -m pip install Pillow --quiet
if errorlevel 1 (
    echo ⚠️ فشل في تثبيت Pillow
)

echo 🔹 تثبيت متطلبات البرنامج...
python -m pip install -r requirements.txt --quiet
if errorlevel 1 (
    echo ⚠️ فشل في تثبيت بعض المتطلبات
)

echo.
echo 🎨 جاري إنشاء الأيقونات والشعارات...
python create_icon.py
if errorlevel 1 (
    echo ⚠️ فشل في إنشاء الأيقونات، سيتم المتابعة بدونها
)

echo.
echo 🔨 جاري بناء ملف EXE...
python build_exe.py
if errorlevel 1 (
    echo ❌ فشل في بناء ملف EXE
    pause
    exit /b 1
)

echo.
echo 📁 جاري تنظيم ملفات التوزيع...

REM إنشاء مجلد التوزيع النهائي
if not exist "release" mkdir release
if not exist "release\الوعد الصادق 4" mkdir "release\الوعد الصادق 4"

REM نسخ ملفات EXE
if exist "dist\الوعد الصادق 4" (
    echo 📄 نسخ ملفات التطبيق...
    xcopy /E /I /Y "dist\الوعد الصادق 4\*" "release\الوعد الصادق 4\" > nul
)

REM نسخ ملفات التوثيق
echo 📚 نسخ ملفات التوثيق...
copy "README.md" "release\الوعد الصادق 4\" > nul 2>&1
copy "INSTALLATION_GUIDE.md" "release\الوعد الصادق 4\" > nul 2>&1
copy "PROJECT_SUMMARY.md" "release\الوعد الصادق 4\" > nul 2>&1
copy "LICENSE.txt" "release\الوعد الصادق 4\" > nul 2>&1

REM نسخ ملف التثبيت
echo 🔧 نسخ ملف التثبيت...
copy "installer.bat" "release\" > nul 2>&1

REM إنشاء ملفات فارغة للمجلدات
echo. > "release\الوعد الصادق 4\data\.gitkeep"
echo. > "release\الوعد الصادق 4\backups\.gitkeep"
echo. > "release\الوعد الصادق 4\reports\.gitkeep"

REM إنشاء ملف معلومات الإصدار
echo 📝 إنشاء ملف معلومات الإصدار...
(
echo الوعد الصادق 4 - الإصدار 4.0.0
echo ================================
echo.
echo تاريخ البناء: %date% %time%
echo نوع البناء: تطبيق Windows احترافي
echo.
echo محتويات الحزمة:
echo - الوعد الصادق 4.exe ^(التطبيق الرئيسي^)
echo - ملفات التوثيق والمساعدة
echo - ملف التثبيت التلقائي
echo.
echo بيانات تسجيل الدخول الافتراضية:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo للدعم الفني: <EMAIL>
) > "release\معلومات الإصدار.txt"

echo.
echo 📦 جاري إنشاء ملف مضغوط للتوزيع...

REM إنشاء ملف ZIP للتوزيع
powershell -Command "Compress-Archive -Path 'release\*' -DestinationPath 'الوعد_الصادق_4_v4.0.0.zip' -Force" > nul 2>&1
if errorlevel 1 (
    echo ⚠️ فشل في إنشاء ملف ZIP، يمكنك ضغط المجلد يدوياً
) else (
    echo ✅ تم إنشاء ملف ZIP للتوزيع
)

echo.
echo 📊 إحصائيات البناء:
if exist "release\الوعد الصادق 4\الوعد الصادق 4.exe" (
    for %%A in ("release\الوعد الصادق 4\الوعد الصادق 4.exe") do (
        echo    📦 حجم التطبيق: %%~zA بايت
    )
)

echo    📁 مجلد الإخراج: release\
if exist "الوعد_الصادق_4_v4.0.0.zip" (
    for %%A in ("الوعد_الصادق_4_v4.0.0.zip") do (
        echo    📦 حجم ملف التوزيع: %%~zA بايت
    )
)

echo.
echo ================================================
echo 🎉 تم بناء التطبيق الاحترافي بنجاح!
echo ================================================
echo.
echo 📁 الملفات الناتجة:
echo    📦 release\الوعد الصادق 4\ - مجلد التطبيق
echo    🔧 release\installer.bat - ملف التثبيت
if exist "الوعد_الصادق_4_v4.0.0.zip" (
    echo    📦 الوعد_الصادق_4_v4.0.0.zip - ملف التوزيع
)
echo.
echo 🚀 يمكنك الآن:
echo    1. توزيع مجلد release\ كاملاً
echo    2. استخدام installer.bat للتثبيت التلقائي
if exist "الوعد_الصادق_4_v4.0.0.zip" (
    echo    3. توزيع ملف ZIP المضغوط
)
echo.
echo 📋 بيانات تسجيل الدخول:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.

set /p open_folder="هل تريد فتح مجلد الإخراج؟ (y/n): "
if /i "%open_folder%"=="y" (
    explorer release
)

echo.
echo 🎉 شكراً لاستخدام الوعد الصادق 4!
pause
