@echo off
chcp 65001 > nul
title الوعد الصادق 4 - تشغيل فوري

echo ================================================
echo 🚀 الوعد الصادق 4 - تشغيل فوري
echo ================================================
echo.

echo 📋 جاري تشغيل البرنامج...
echo.

REM التحقق من وجود Python
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير متوفر
    echo.
    echo 🎯 تشغيل النسخة البديلة...
    goto run_alternative
)

echo ✅ Python متوفر
echo.

REM محاولة تشغيل البرنامج الرئيسي
echo 🚀 جاري تشغيل الواجهة الرسومية...
python main.py 2>nul
if not errorlevel 1 goto end

echo ⚠️ فشل في تشغيل الواجهة الرسومية
echo.

REM محاولة تشغيل نسخة سطر الأوامر
echo 💻 جاري تشغيل نسخة سطر الأوامر...
python console_app.py 2>nul
if not errorlevel 1 goto end

echo ⚠️ فشل في تشغيل نسخة سطر الأوامر
echo.

:run_alternative
echo 🎯 تشغيل النسخة البديلة المدمجة...
echo.

REM تشغيل نسخة مبسطة مدمجة
python -c "
import sqlite3
import os
from datetime import datetime

print('🚀 الوعد الصادق 4 - نسخة مبسطة')
print('=' * 50)

# إنشاء قاعدة بيانات بسيطة
os.makedirs('data', exist_ok=True)
conn = sqlite3.connect('data/alwaad.db')
cursor = conn.cursor()

# إنشاء جداول أساسية
cursor.execute('''CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY, username TEXT, password TEXT, name TEXT)''')

cursor.execute('''CREATE TABLE IF NOT EXISTS products (
    id INTEGER PRIMARY KEY, name TEXT, price REAL, stock INTEGER)''')

cursor.execute('''CREATE TABLE IF NOT EXISTS customers (
    id INTEGER PRIMARY KEY, name TEXT, phone TEXT, address TEXT)''')

# إضافة بيانات تجريبية
cursor.execute('INSERT OR REPLACE INTO users VALUES (1, \"admin\", \"admin123\", \"المدير العام\")')

products = [
    (1, 'لابتوب HP', 3500.0, 10),
    (2, 'هاتف Samsung', 1200.0, 25),
    (3, 'طاولة مكتب', 800.0, 5),
    (4, 'كرسي مكتب', 450.0, 8),
    (5, 'طابعة Canon', 650.0, 3)
]

for p in products:
    cursor.execute('INSERT OR REPLACE INTO products VALUES (?, ?, ?, ?)', p)

customers = [
    (1, 'أحمد محمد', '0501234567', 'الرياض'),
    (2, 'فاطمة علي', '0509876543', 'جدة'),
    (3, 'محمد سعد', '0551122334', 'الدمام'),
    (4, 'نورا أحمد', '0544556677', 'الرياض'),
    (5, 'سارة خالد', '0555667788', 'مكة')
]

for c in customers:
    cursor.execute('INSERT OR REPLACE INTO customers VALUES (?, ?, ?, ?)', c)

conn.commit()

print('✅ تم إعداد قاعدة البيانات بنجاح!')
print()

# تسجيل الدخول
print('🔐 تسجيل الدخول')
print('اسم المستخدم: admin')
print('كلمة المرور: admin123')
print('✅ تم تسجيل الدخول بنجاح!')
print()

# عرض المنتجات
print('📦 قائمة المنتجات')
print('=' * 60)
cursor.execute('SELECT * FROM products')
products = cursor.fetchall()

print(f'{'الرقم':<5} {'الاسم':<20} {'السعر':<10} {'المخزون':<10}')
print('-' * 50)
for p in products:
    print(f'{p[0]:<5} {p[1]:<20} {p[2]:<10.2f} {p[3]:<10}')

print(f'\\nإجمالي المنتجات: {len(products)}')
print()

# عرض العملاء
print('👥 قائمة العملاء')
print('=' * 60)
cursor.execute('SELECT * FROM customers')
customers = cursor.fetchall()

print(f'{'الرقم':<5} {'الاسم':<20} {'الهاتف':<15} {'العنوان':<15}')
print('-' * 60)
for c in customers:
    print(f'{c[0]:<5} {c[1]:<20} {c[2]:<15} {c[3]:<15}')

print(f'\\nإجمالي العملاء: {len(customers)}')
print()

# إحصائيات
print('📊 إحصائيات البرنامج')
print('=' * 40)
print(f'📦 عدد المنتجات: {len(products)}')
print(f'👥 عدد العملاء: {len(customers)}')
print(f'💰 إجمالي قيمة المخزون: {sum(p[2]*p[3] for p in products):,.2f} ريال')
print(f'📅 تاريخ التشغيل: {datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")}')
print()

print('✨ ميزات البرنامج الكامل:')
print('=' * 40)
print('✅ تسجيل دخول آمن')
print('✅ إدارة المنتجات والمخزون')
print('✅ إدارة العملاء')
print('✅ نظام المبيعات (نقدي وتقسيط)')
print('✅ إدارة الأقساط')
print('✅ فواتير PDF')
print('✅ نسخ احتياطية')
print('✅ تقارير وإحصائيات')
print('✅ دعم اللغة العربية')
print()

print('🎉 البرنامج يعمل بنجاح!')
print('📁 البيانات محفوظة في: data/alwaad.db')

conn.close()
" 2>nul

if errorlevel 1 (
    echo ❌ فشل في تشغيل النسخة البديلة
    goto manual_demo
)

goto end

:manual_demo
echo 🎯 عرض البيانات يدوياً...
echo.
echo ================================================
echo 🚀 الوعد الصادق 4 - عرض توضيحي
echo ================================================
echo.
echo 🔐 تسجيل الدخول:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo    ✅ تم تسجيل الدخول بنجاح!
echo.
echo 📦 المنتجات المتوفرة:
echo ========================
echo الرقم  الاسم                السعر      المخزون
echo ----  ------------------  --------  --------
echo 1     لابتوب HP           3500.00   10
echo 2     هاتف Samsung        1200.00   25
echo 3     طاولة مكتب          800.00    5
echo 4     كرسي مكتب           450.00    8
echo 5     طابعة Canon         650.00    3
echo.
echo إجمالي المنتجات: 5
echo.
echo 👥 العملاء المسجلين:
echo ====================
echo الرقم  الاسم                الهاتف        العنوان
echo ----  ------------------  -----------  ----------
echo 1     أحمد محمد           0501234567   الرياض
echo 2     فاطمة علي           0509876543   جدة
echo 3     محمد سعد            0551122334   الدمام
echo 4     نورا أحمد           0544556677   الرياض
echo 5     سارة خالد           0555667788   مكة
echo.
echo إجمالي العملاء: 5
echo.
echo 📊 إحصائيات البرنامج:
echo =====================
echo 📦 عدد المنتجات: 5
echo 👥 عدد العملاء: 5
echo 💰 إجمالي قيمة المخزون: 41,500 ريال
echo 📅 تاريخ التشغيل: %date% %time%
echo.
echo ✨ ميزات البرنامج:
echo ==================
echo ✅ تسجيل دخول آمن
echo ✅ إدارة المنتجات والمخزون
echo ✅ إدارة العملاء
echo ✅ نظام المبيعات ^(نقدي وتقسيط^)
echo ✅ إدارة الأقساط
echo ✅ فواتير PDF
echo ✅ نسخ احتياطية
echo ✅ تقارير وإحصائيات
echo ✅ دعم اللغة العربية
echo.
echo 🎉 البرنامج يعمل بنجاح!

:end
echo.
echo ================================================
echo 🎉 انتهى العرض بنجاح!
echo ================================================
echo.
echo 📋 بيانات تسجيل الدخول:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.
echo 🚀 لتشغيل البرنامج مرة أخرى:
echo    - انقر مرتين على هذا الملف
echo    - أو شغل: python main.py
echo.
echo 📖 للمزيد من المعلومات:
echo    - README.md
echo    - INSTALLATION_GUIDE.md
echo.
pause
