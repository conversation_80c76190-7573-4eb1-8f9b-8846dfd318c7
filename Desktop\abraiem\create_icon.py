# -*- coding: utf-8 -*-
"""
إنشاء أيقونة لبرنامج الوعد الصادق 4
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_app_icon():
    """إنشاء أيقونة البرنامج"""
    # إنشاء مجلد الموارد
    os.makedirs("resources", exist_ok=True)
    
    # أحجام الأيقونات المطلوبة
    sizes = [16, 32, 48, 64, 128, 256]
    
    # إنشاء أيقونة بسيطة
    def create_icon_image(size):
        # إنشاء صورة جديدة
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # رسم خلفية دائرية
        margin = size // 8
        draw.ellipse([margin, margin, size-margin, size-margin], 
                    fill=(52, 152, 219, 255), outline=(41, 128, 185, 255), width=2)
        
        # رسم رمز المال (₹)
        font_size = size // 3
        try:
            # محاولة استخدام خط عربي
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            font = ImageFont.load_default()
        
        # رسم النص
        text = "₹"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size - text_width) // 2
        y = (size - text_height) // 2 - font_size // 8
        
        draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
        
        return img
    
    # إنشاء أيقونات بأحجام مختلفة
    icons = []
    for size in sizes:
        icon = create_icon_image(size)
        icons.append(icon)
        
        # حفظ كل حجم منفصل
        icon.save(f"resources/icon_{size}x{size}.png")
    
    # حفظ كملف ICO
    icons[0].save("resources/icon.ico", format='ICO', sizes=[(s, s) for s in sizes])
    
    # حفظ كـ PNG رئيسي
    icons[-1].save("resources/icon.png")
    
    print("✅ تم إنشاء أيقونة البرنامج")
    print("📁 الملفات المنشأة:")
    print("   🖼️ resources/icon.ico - أيقونة Windows")
    print("   🖼️ resources/icon.png - أيقونة PNG")
    for size in sizes:
        print(f"   🖼️ resources/icon_{size}x{size}.png")

def create_logo():
    """إنشاء شعار البرنامج"""
    # إنشاء شعار أكبر للاستخدام في البرنامج
    size = 512
    img = Image.new('RGBA', (size, size), (255, 255, 255, 255))
    draw = ImageDraw.Draw(img)
    
    # رسم خلفية متدرجة (محاكاة)
    for i in range(size):
        color_value = int(255 - (i / size) * 50)
        draw.line([(0, i), (size, i)], fill=(color_value, color_value, 255, 255))
    
    # رسم إطار
    border_width = 10
    draw.rectangle([border_width, border_width, size-border_width, size-border_width], 
                  outline=(52, 152, 219, 255), width=border_width)
    
    # رسم النص العربي (محاكاة)
    font_size = size // 8
    try:
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        font = ImageFont.load_default()
    
    # النص الرئيسي
    text1 = "الوعد الصادق 4"
    bbox1 = draw.textbbox((0, 0), text1, font=font)
    text1_width = bbox1[2] - bbox1[0]
    x1 = (size - text1_width) // 2
    y1 = size // 3
    
    draw.text((x1, y1), text1, fill=(52, 73, 94, 255), font=font)
    
    # النص الفرعي
    font_size_small = size // 12
    try:
        font_small = ImageFont.truetype("arial.ttf", font_size_small)
    except:
        font_small = ImageFont.load_default()
    
    text2 = "نظام إدارة المبيعات والتقسيط"
    bbox2 = draw.textbbox((0, 0), text2, font=font_small)
    text2_width = bbox2[2] - bbox2[0]
    x2 = (size - text2_width) // 2
    y2 = y1 + font_size + 20
    
    draw.text((x2, y2), text2, fill=(127, 140, 141, 255), font=font_small)
    
    # رسم رموز
    symbol_size = size // 6
    symbols = ["💰", "📊", "📋"]
    symbol_y = y2 + font_size_small + 40
    
    for i, symbol in enumerate(symbols):
        x_pos = (size // 4) + i * (size // 4)
        try:
            symbol_font = ImageFont.truetype("seguiemj.ttf", symbol_size)
            draw.text((x_pos, symbol_y), symbol, font=symbol_font)
        except:
            # رسم دوائر بدلاً من الرموز
            circle_size = symbol_size // 2
            draw.ellipse([x_pos, symbol_y, x_pos + circle_size, symbol_y + circle_size],
                        fill=(52, 152, 219, 255))
    
    # حفظ الشعار
    img.save("resources/logo.png")
    
    print("✅ تم إنشاء شعار البرنامج")
    print("   🖼️ resources/logo.png")

def main():
    """إنشاء جميع الصور المطلوبة"""
    print("🎨 جاري إنشاء أيقونات وشعارات البرنامج...")
    print("=" * 50)
    
    try:
        create_app_icon()
        create_logo()
        
        print("=" * 50)
        print("🎉 تم إنشاء جميع الصور بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الصور: {e}")
        print("⚠️ تأكد من تثبيت مكتبة Pillow:")
        print("   pip install Pillow")

if __name__ == "__main__":
    main()
