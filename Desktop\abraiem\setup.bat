@echo off
chcp 65001 > nul
title تثبيت برنامج الوعد الصادق 4

echo ================================================
echo 🔧 تثبيت برنامج الوعد الصادق 4
echo ================================================
echo.

echo 🔍 جاري التحقق من Python...
python --version
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo.
    echo 📥 يرجى تحميل وتثبيت Python من:
    echo https://www.python.org/downloads/
    echo.
    echo ⚠️ تأكد من تفعيل خيار "Add Python to PATH"
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

echo 📦 جاري تحديث pip...
python -m pip install --upgrade pip
if errorlevel 1 (
    echo ⚠️ فشل في تحديث pip، سيتم المتابعة...
)

echo.
echo 📦 جاري تثبيت المتطلبات الأساسية...

echo 🔹 تثبيت PyQt5...
python -m pip install PyQt5
if errorlevel 1 (
    echo ⚠️ فشل في تثبيت PyQt5، جرب:
    echo python -m pip install PyQt5 --user
)

echo 🔹 تثبيت reportlab...
python -m pip install reportlab
if errorlevel 1 (
    echo ⚠️ فشل في تثبيت reportlab
)

echo 🔹 تثبيت bcrypt...
python -m pip install bcrypt
if errorlevel 1 (
    echo ⚠️ فشل في تثبيت bcrypt
)

echo 🔹 تثبيت المتطلبات الإضافية...
python -m pip install arabic-reshaper python-bidi openpyxl Pillow
if errorlevel 1 (
    echo ⚠️ فشل في تثبيت بعض المتطلبات الإضافية
)

echo.
echo 📁 جاري إنشاء المجلدات...
if not exist "data" mkdir data
if not exist "backups" mkdir backups
if not exist "reports" mkdir reports
if not exist "resources" mkdir resources
if not exist "logs" mkdir logs

echo ✅ تم إنشاء المجلدات
echo.

echo 🧪 جاري اختبار التثبيت...
python -c "import sqlite3; print('✅ SQLite متوفر')"
python -c "import PyQt5; print('✅ PyQt5 متوفر')" 2>nul || echo "⚠️ PyQt5 غير متوفر"
python -c "import reportlab; print('✅ ReportLab متوفر')" 2>nul || echo "⚠️ ReportLab غير متوفر"

echo.
echo 🎉 تم التثبيت!
echo.
echo 📋 بيانات تسجيل الدخول الافتراضية:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.
echo 🚀 لتشغيل البرنامج:
echo    - انقر مرتين على run.bat
echo    أو
echo    - python main.py
echo.
echo 📖 لمزيد من المعلومات، راجع:
echo    - README.md
echo    - INSTALLATION_GUIDE.md
echo.

set /p choice="هل تريد تشغيل البرنامج الآن؟ (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 🚀 جاري تشغيل البرنامج...
    call run.bat
) else (
    echo.
    echo 👋 شكراً لك! يمكنك تشغيل البرنامج لاحقاً بالنقر على run.bat
)

pause
