# الوعد الصادق 4 - نظام إدارة المبيعات والتقسيط

## نظرة عامة
برنامج الوعد الصادق 4 هو نظام شامل لإدارة المبيعات والتقسيط مصمم خصيصاً للشركات والمتاجر التي تتعامل مع البيع النقدي والتقسيط. يوفر البرنامج واجهة عربية سهلة الاستخدام مع دعم كامل للغة العربية.

## المميزات الرئيسية

### 🔐 إدارة المستخدمين
- تسجيل دخول آمن بكلمة مرور مشفرة
- صلاحيات متعددة (مدير - موظف مبيعات)
- تتبع جلسات المستخدمين

### 📦 إدارة المنتجات
- إضافة وتعديل وحذف المنتجات
- تتبع المخزون والكميات
- تصنيف المنتجات
- تنبيهات المخزون المنخفض

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- تتبع تاريخ التعاملات
- إدارة حدود الائتمان
- سجل الأقساط لكل عميل

### 💰 نظام المبيعات
- دعم البيع النقدي والتقسيط
- حساب الأقساط الشهرية تلقائياً
- إدارة الدفعات الأولى والفوائد
- فواتير قابلة للطباعة

### 📅 إدارة الأقساط
- جدولة الأقساط التلقائية
- تتبع المدفوعات
- تنبيهات الأقساط المستحقة
- تقارير الأقساط المتأخرة

### 📊 التقارير والإحصائيات
- تقارير المبيعات اليومية والشهرية
- تحليل الأرباح والخسائر
- تقارير العملاء والأقساط
- تصدير التقارير بصيغة PDF

### 🛡️ الأمان والنسخ الاحتياطية
- تشفير كلمات المرور
- نسخ احتياطية تلقائية ويدوية
- حماية البيانات
- استعادة النسخ الاحتياطية

## متطلبات النظام

### الحد الأدنى
- نظام التشغيل: Windows 7 أو أحدث
- المعالج: Intel Core i3 أو معادل
- الذاكرة: 4 جيجابايت RAM
- مساحة القرص: 500 ميجابايت
- Python 3.6 أو أحدث

### المستحسن
- نظام التشغيل: Windows 10 أو أحدث
- المعالج: Intel Core i5 أو أحدث
- الذاكرة: 8 جيجابايت RAM
- مساحة القرص: 2 جيجابايت
- Python 3.8 أو أحدث

## التثبيت والتشغيل

### 1. تثبيت Python
```bash
# تحميل Python من الموقع الرسمي
https://www.python.org/downloads/
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل البرنامج
```bash
python main.py
```

## بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

⚠️ **تنبيه:** يُنصح بتغيير كلمة المرور الافتراضية فور تسجيل الدخول الأول.

## هيكل المشروع

```
الوعد الصادق 4/
├── main.py                 # الملف الرئيسي
├── requirements.txt        # متطلبات Python
├── config/                 # ملفات الإعدادات
│   └── settings.py
├── database/              # إدارة قاعدة البيانات
│   └── database_manager.py
├── models/                # نماذج البيانات
│   ├── user_model.py
│   ├── product_model.py
│   ├── customer_model.py
│   └── sales_model.py
├── ui/                    # واجهات المستخدم
│   ├── login_window.py
│   └── main_window.py
├── utils/                 # أدوات مساعدة
│   ├── pdf_generator.py
│   └── backup_manager.py
├── data/                  # قاعدة البيانات
├── backups/              # النسخ الاحتياطية
├── reports/              # التقارير المحفوظة
└── resources/            # الصور والأيقونات
```

## الاستخدام

### تسجيل الدخول
1. شغل البرنامج
2. أدخل اسم المستخدم وكلمة المرور
3. اضغط "تسجيل الدخول"

### إضافة منتج جديد
1. انتقل إلى "إدارة المنتجات"
2. اضغط "إضافة منتج"
3. املأ البيانات المطلوبة
4. احفظ المنتج

### إنشاء عملية بيع
1. انتقل إلى "المبيعات"
2. اختر "بيع جديد"
3. اختر العميل والمنتجات
4. حدد نوع البيع (نقدي/تقسيط)
5. أكمل العملية

### إدارة الأقساط
1. انتقل إلى "إدارة الأقساط"
2. اعرض الأقساط المستحقة
3. سجل المدفوعات
4. اطبع جدول الأقساط

## النسخ الاحتياطية

### إنشاء نسخة احتياطية يدوية
1. انتقل إلى "الإعدادات"
2. اختر "النسخ الاحتياطية"
3. اضغط "إنشاء نسخة احتياطية"

### استعادة نسخة احتياطية
1. انتقل إلى "الإعدادات"
2. اختر "النسخ الاحتياطية"
3. اختر النسخة المطلوبة
4. اضغط "استعادة"

## الدعم الفني

### المشاكل الشائعة

**مشكلة:** البرنامج لا يبدأ
**الحل:** تأكد من تثبيت Python وجميع المتطلبات

**مشكلة:** خطأ في قاعدة البيانات
**الحل:** استعد نسخة احتياطية أو أعد إنشاء قاعدة البيانات

**مشكلة:** مشاكل في الطباعة
**الحل:** تأكد من تثبيت مكتبات PDF المطلوبة

### تسجيل الأخطاء
يتم حفظ سجل الأخطاء في ملف `logs/error.log`

## التطوير والمساهمة

### إضافة ميزة جديدة
1. أنشئ فرع جديد
2. طور الميزة
3. اختبر التغييرات
4. أرسل طلب دمج

### اختبار البرنامج
```bash
python -m pytest tests/
```

## الترخيص
هذا البرنامج محمي بحقوق الطبع والنشر © 2024 الوعد الصادق 4

## معلومات الاتصال
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +966 XX XXX XXXX
- **الموقع:** www.alwaad.com

## سجل التحديثات

### الإصدار 4.0 (2024)
- إعادة تصميم كاملة للواجهة
- دعم محسن للغة العربية
- نظام أقساط متطور
- تقارير PDF محسنة
- نسخ احتياطية تلقائية
- أمان محسن

---

**شكراً لاستخدام برنامج الوعد الصادق 4!**
