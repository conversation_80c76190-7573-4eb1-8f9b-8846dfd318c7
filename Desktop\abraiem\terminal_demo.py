#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي لبرنامج الوعد الصادق 4 في الترمنل
"""

import sqlite3
import os
import hashlib
from datetime import datetime, timedelta
from pathlib import Path

class AlwaadAlsadiqDemo:
    def __init__(self):
        self.db_path = "data/demo.db"
        self.current_user = None
        self.setup_database()
        
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        # إنشاء مجلد البيانات
        os.makedirs("data", exist_ok=True)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY,
                username TEXT UNIQUE,
                password TEXT,
                full_name TEXT,
                role TEXT
            )
        ''')
        
        # جدول المنتجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY,
                name TEXT,
                price REAL,
                stock INTEGER,
                category TEXT
            )
        ''')
        
        # جدول العملاء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY,
                name TEXT,
                phone TEXT,
                address TEXT
            )
        ''')
        
        # جدول المبيعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales (
                id INTEGER PRIMARY KEY,
                customer_id INTEGER,
                product_id INTEGER,
                quantity INTEGER,
                total_amount REAL,
                sale_type TEXT,
                sale_date TEXT
            )
        ''')
        
        # إضافة بيانات تجريبية
        self.add_sample_data(cursor)
        
        conn.commit()
        conn.close()
        
    def add_sample_data(self, cursor):
        """إضافة بيانات تجريبية"""
        # مستخدم افتراضي
        cursor.execute('''
            INSERT OR REPLACE INTO users (id, username, password, full_name, role)
            VALUES (1, 'admin', 'admin123', 'المدير العام', 'admin')
        ''')
        
        # منتجات تجريبية
        products = [
            (1, 'لابتوب HP', 3500.0, 10, 'إلكترونيات'),
            (2, 'هاتف Samsung', 1200.0, 25, 'إلكترونيات'),
            (3, 'طاولة مكتب', 800.0, 5, 'أثاث'),
            (4, 'كرسي مكتب', 450.0, 8, 'أثاث'),
            (5, 'طابعة Canon', 650.0, 3, 'إلكترونيات')
        ]
        
        for product in products:
            cursor.execute('''
                INSERT OR REPLACE INTO products (id, name, price, stock, category)
                VALUES (?, ?, ?, ?, ?)
            ''', product)
        
        # عملاء تجريبيون
        customers = [
            (1, 'أحمد محمد', '0501234567', 'الرياض'),
            (2, 'فاطمة علي', '0509876543', 'جدة'),
            (3, 'محمد سعد', '0551122334', 'الدمام'),
            (4, 'نورا أحمد', '0544556677', 'الرياض')
        ]
        
        for customer in customers:
            cursor.execute('''
                INSERT OR REPLACE INTO customers (id, name, phone, address)
                VALUES (?, ?, ?, ?)
            ''', customer)
        
        # مبيعات تجريبية
        sales = [
            (1, 1, 1, 1, 3500.0, 'تقسيط', '2024-01-15'),
            (2, 2, 2, 2, 2400.0, 'نقدي', '2024-01-16'),
            (3, 3, 3, 1, 800.0, 'نقدي', '2024-01-17'),
            (4, 1, 4, 2, 900.0, 'تقسيط', '2024-01-18')
        ]
        
        for sale in sales:
            cursor.execute('''
                INSERT OR REPLACE INTO sales (id, customer_id, product_id, quantity, total_amount, sale_type, sale_date)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', sale)
    
    def login(self, username, password):
        """تسجيل الدخول"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, full_name, role
            FROM users 
            WHERE username = ? AND password = ?
        ''', (username, password))
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            self.current_user = {
                'id': user[0],
                'username': user[1],
                'full_name': user[2],
                'role': user[3]
            }
            return True
        return False
    
    def show_products(self):
        """عرض المنتجات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM products ORDER BY name')
        products = cursor.fetchall()
        conn.close()
        
        print("\n" + "="*80)
        print("📦 قائمة المنتجات")
        print("="*80)
        print(f"{'الرقم':<5} {'الاسم':<20} {'السعر':<10} {'المخزون':<10} {'الفئة':<15}")
        print("-"*80)
        
        for product in products:
            print(f"{product[0]:<5} {product[1]:<20} {product[2]:<10.2f} {product[3]:<10} {product[4]:<15}")
        
        print(f"\nإجمالي المنتجات: {len(products)}")
    
    def show_customers(self):
        """عرض العملاء"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM customers ORDER BY name')
        customers = cursor.fetchall()
        conn.close()
        
        print("\n" + "="*80)
        print("👥 قائمة العملاء")
        print("="*80)
        print(f"{'الرقم':<5} {'الاسم':<20} {'الهاتف':<15} {'العنوان':<20}")
        print("-"*80)
        
        for customer in customers:
            print(f"{customer[0]:<5} {customer[1]:<20} {customer[2]:<15} {customer[3]:<20}")
        
        print(f"\nإجمالي العملاء: {len(customers)}")
    
    def show_sales(self):
        """عرض المبيعات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT s.id, c.name, p.name, s.quantity, s.total_amount, s.sale_type, s.sale_date
            FROM sales s
            JOIN customers c ON s.customer_id = c.id
            JOIN products p ON s.product_id = p.id
            ORDER BY s.sale_date DESC
        ''')
        sales = cursor.fetchall()
        conn.close()
        
        print("\n" + "="*100)
        print("💰 قائمة المبيعات")
        print("="*100)
        print(f"{'الرقم':<5} {'العميل':<15} {'المنتج':<15} {'الكمية':<8} {'المبلغ':<10} {'النوع':<10} {'التاريخ':<12}")
        print("-"*100)
        
        total_sales = 0
        for sale in sales:
            print(f"{sale[0]:<5} {sale[1]:<15} {sale[2]:<15} {sale[3]:<8} {sale[4]:<10.2f} {sale[5]:<10} {sale[6]:<12}")
            total_sales += sale[4]
        
        print(f"\nإجمالي المبيعات: {len(sales)} عملية")
        print(f"إجمالي المبلغ: {total_sales:.2f} ريال")
    
    def show_statistics(self):
        """عرض الإحصائيات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # إحصائيات عامة
        cursor.execute('SELECT COUNT(*) FROM products')
        products_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM customers')
        customers_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM sales')
        sales_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT SUM(total_amount) FROM sales')
        total_revenue = cursor.fetchone()[0] or 0
        
        cursor.execute('SELECT COUNT(*) FROM sales WHERE sale_type = "تقسيط"')
        installment_sales = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM sales WHERE sale_type = "نقدي"')
        cash_sales = cursor.fetchone()[0]
        
        conn.close()
        
        print("\n" + "="*60)
        print("📊 إحصائيات البرنامج")
        print("="*60)
        print(f"📦 عدد المنتجات: {products_count}")
        print(f"👥 عدد العملاء: {customers_count}")
        print(f"💰 عدد المبيعات: {sales_count}")
        print(f"💵 إجمالي الإيرادات: {total_revenue:.2f} ريال")
        print(f"📅 المبيعات بالتقسيط: {installment_sales}")
        print(f"💸 المبيعات النقدية: {cash_sales}")
        print("="*60)
    
    def add_product_demo(self):
        """إضافة منتج تجريبي"""
        print("\n➕ إضافة منتج جديد")
        print("-" * 30)
        
        # بيانات منتج تجريبي
        name = "منتج تجريبي جديد"
        price = 299.99
        stock = 15
        category = "تجريبي"
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO products (name, price, stock, category)
            VALUES (?, ?, ?, ?)
        ''', (name, price, stock, category))
        
        product_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        print(f"✅ تم إضافة المنتج بنجاح!")
        print(f"   الرقم: {product_id}")
        print(f"   الاسم: {name}")
        print(f"   السعر: {price} ريال")
        print(f"   المخزون: {stock}")
        print(f"   الفئة: {category}")
    
    def create_backup_demo(self):
        """إنشاء نسخة احتياطية تجريبية"""
        print("\n💾 إنشاء نسخة احتياطية")
        print("-" * 30)
        
        # إنشاء مجلد النسخ الاحتياطية
        os.makedirs("backups", exist_ok=True)
        
        # نسخ قاعدة البيانات
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"backup_{timestamp}.db"
        backup_path = f"backups/{backup_name}"
        
        import shutil
        shutil.copy2(self.db_path, backup_path)
        
        file_size = os.path.getsize(backup_path)
        
        print(f"✅ تم إنشاء النسخة الاحتياطية بنجاح!")
        print(f"   اسم الملف: {backup_name}")
        print(f"   المسار: {backup_path}")
        print(f"   الحجم: {file_size} بايت")
        print(f"   التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def run_demo(self):
        """تشغيل العرض التوضيحي"""
        print("🚀 مرحباً بك في برنامج الوعد الصادق 4")
        print("=" * 50)
        print("📱 عرض توضيحي تفاعلي")
        print("=" * 50)
        
        # تسجيل الدخول
        print("\n🔐 تسجيل الدخول")
        print("بيانات الدخول الافتراضية:")
        print("اسم المستخدم: admin")
        print("كلمة المرور: admin123")
        
        if self.login("admin", "admin123"):
            print(f"✅ مرحباً {self.current_user['full_name']}!")
            print(f"الصلاحية: {self.current_user['role']}")
        else:
            print("❌ فشل في تسجيل الدخول")
            return
        
        # عرض البيانات
        self.show_statistics()
        self.show_products()
        self.show_customers()
        self.show_sales()
        
        # إضافة منتج جديد
        self.add_product_demo()
        
        # إنشاء نسخة احتياطية
        self.create_backup_demo()
        
        print("\n🎉 انتهى العرض التوضيحي بنجاح!")
        print("=" * 50)
        print("📋 ملخص ما تم عرضه:")
        print("✅ تسجيل دخول آمن")
        print("✅ عرض المنتجات والعملاء")
        print("✅ عرض المبيعات والإحصائيات")
        print("✅ إضافة منتج جديد")
        print("✅ إنشاء نسخة احتياطية")
        print("=" * 50)
        print("🚀 البرنامج الكامل جاهز للاستخدام!")

def main():
    """تشغيل العرض التوضيحي"""
    try:
        demo = AlwaadAlsadiqDemo()
        demo.run_demo()
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
