# -*- coding: utf-8 -*-
"""
مدير النسخ الاحتياطية لبرنامج الوعد الصادق 4
"""

import os
import shutil
import zipfile
from datetime import datetime, timedelta
from pathlib import Path
from config.settings import DATABASE_CONFIG, BACKUP_DIR, BASE_DIR
from database.database_manager import db_manager

class BackupManager:
    def __init__(self):
        self.backup_dir = Path(BACKUP_DIR)
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
    def create_full_backup(self, include_reports=True):
        """إنشاء نسخة احتياطية كاملة"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"full_backup_{timestamp}"
        backup_path = self.backup_dir / f"{backup_name}.zip"
        
        try:
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # نسخ قاعدة البيانات
                db_path = Path(DATABASE_CONFIG['path'])
                if db_path.exists():
                    zipf.write(db_path, f"database/{db_path.name}")
                
                # نسخ ملفات الإعدادات
                config_dir = BASE_DIR / "config"
                if config_dir.exists():
                    for file_path in config_dir.rglob("*.py"):
                        arcname = f"config/{file_path.relative_to(config_dir)}"
                        zipf.write(file_path, arcname)
                
                # نسخ التقارير (اختياري)
                if include_reports:
                    reports_dir = BASE_DIR / "reports"
                    if reports_dir.exists():
                        for file_path in reports_dir.rglob("*"):
                            if file_path.is_file():
                                arcname = f"reports/{file_path.relative_to(reports_dir)}"
                                zipf.write(file_path, arcname)
                
                # إضافة معلومات النسخة الاحتياطية
                backup_info = self._create_backup_info()
                zipf.writestr("backup_info.txt", backup_info)
            
            return str(backup_path)
            
        except Exception as e:
            if backup_path.exists():
                backup_path.unlink()
            raise Exception(f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")
    
    def create_database_backup(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات فقط"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"database_backup_{timestamp}.db"
        
        try:
            backup_path = db_manager.backup_database(backup_name)
            return backup_path
            
        except Exception as e:
            raise Exception(f"فشل في إنشاء نسخة احتياطية من قاعدة البيانات: {str(e)}")
    
    def restore_full_backup(self, backup_path):
        """استعادة نسخة احتياطية كاملة"""
        backup_file = Path(backup_path)
        
        if not backup_file.exists():
            raise Exception("ملف النسخة الاحتياطية غير موجود")
        
        if not backup_file.suffix.lower() == '.zip':
            raise Exception("ملف النسخة الاحتياطية يجب أن يكون بصيغة ZIP")
        
        try:
            # إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة
            current_backup = self.create_full_backup()
            
            with zipfile.ZipFile(backup_file, 'r') as zipf:
                # استعادة قاعدة البيانات
                db_files = [f for f in zipf.namelist() if f.startswith('database/') and f.endswith('.db')]
                if db_files:
                    db_file = db_files[0]
                    zipf.extract(db_file, self.backup_dir)
                    extracted_db = self.backup_dir / db_file
                    
                    # استعادة قاعدة البيانات
                    db_manager.restore_database(str(extracted_db))
                    
                    # حذف الملف المؤقت
                    extracted_db.unlink()
                    shutil.rmtree(self.backup_dir / "database", ignore_errors=True)
                
                # استعادة التقارير
                report_files = [f for f in zipf.namelist() if f.startswith('reports/')]
                if report_files:
                    reports_dir = BASE_DIR / "reports"
                    reports_dir.mkdir(parents=True, exist_ok=True)
                    
                    for report_file in report_files:
                        zipf.extract(report_file, BASE_DIR)
            
            return current_backup
            
        except Exception as e:
            raise Exception(f"فشل في استعادة النسخة الاحتياطية: {str(e)}")
    
    def restore_database_backup(self, backup_path):
        """استعادة نسخة احتياطية من قاعدة البيانات"""
        try:
            # إنشاء نسخة احتياطية من قاعدة البيانات الحالية
            current_backup = self.create_database_backup()
            
            # استعادة النسخة الاحتياطية
            db_manager.restore_database(backup_path)
            
            return current_backup
            
        except Exception as e:
            raise Exception(f"فشل في استعادة قاعدة البيانات: {str(e)}")
    
    def get_backup_list(self):
        """الحصول على قائمة النسخ الاحتياطية"""
        backups = []
        
        for backup_file in self.backup_dir.glob("*"):
            if backup_file.is_file() and backup_file.suffix in ['.zip', '.db']:
                stat = backup_file.stat()
                backup_info = {
                    'name': backup_file.name,
                    'path': str(backup_file),
                    'size': stat.st_size,
                    'created_at': datetime.fromtimestamp(stat.st_ctime),
                    'type': 'full' if backup_file.suffix == '.zip' else 'database'
                }
                backups.append(backup_info)
        
        # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
        backups.sort(key=lambda x: x['created_at'], reverse=True)
        
        return backups
    
    def delete_backup(self, backup_path):
        """حذف نسخة احتياطية"""
        backup_file = Path(backup_path)
        
        if not backup_file.exists():
            raise Exception("ملف النسخة الاحتياطية غير موجود")
        
        try:
            backup_file.unlink()
            return True
            
        except Exception as e:
            raise Exception(f"فشل في حذف النسخة الاحتياطية: {str(e)}")
    
    def cleanup_old_backups(self, keep_days=30):
        """تنظيف النسخ الاحتياطية القديمة"""
        cutoff_date = datetime.now() - timedelta(days=keep_days)
        deleted_count = 0
        
        for backup_file in self.backup_dir.glob("*"):
            if backup_file.is_file():
                file_date = datetime.fromtimestamp(backup_file.stat().st_ctime)
                if file_date < cutoff_date:
                    try:
                        backup_file.unlink()
                        deleted_count += 1
                    except:
                        pass
        
        return deleted_count
    
    def auto_backup(self):
        """إنشاء نسخة احتياطية تلقائية"""
        if not DATABASE_CONFIG.get('auto_backup', False):
            return None
        
        # التحقق من آخر نسخة احتياطية
        backups = self.get_backup_list()
        
        if backups:
            last_backup = backups[0]['created_at']
            hours_since_last = (datetime.now() - last_backup).total_seconds() / 3600
            
            if hours_since_last < DATABASE_CONFIG.get('backup_interval', 24):
                return None  # لا حاجة لنسخة احتياطية جديدة
        
        try:
            backup_path = self.create_database_backup()
            
            # تنظيف النسخ القديمة
            self.cleanup_old_backups()
            
            return backup_path
            
        except Exception as e:
            print(f"فشل في إنشاء النسخة الاحتياطية التلقائية: {str(e)}")
            return None
    
    def _create_backup_info(self):
        """إنشاء معلومات النسخة الاحتياطية"""
        info = f"""معلومات النسخة الاحتياطية
========================

تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
اسم البرنامج: الوعد الصادق 4
إصدار البرنامج: 4.0

محتويات النسخة الاحتياطية:
- قاعدة البيانات
- ملفات الإعدادات
- التقارير المحفوظة

ملاحظة: يُنصح بحفظ هذه النسخة في مكان آمن
"""
        return info
    
    def get_backup_size(self):
        """حساب حجم جميع النسخ الاحتياطية"""
        total_size = 0
        
        for backup_file in self.backup_dir.glob("*"):
            if backup_file.is_file():
                total_size += backup_file.stat().st_size
        
        return total_size
    
    def format_file_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 بايت"
        
        size_names = ["بايت", "كيلوبايت", "ميجابايت", "جيجابايت"]
        i = 0
        
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.2f} {size_names[i]}"

# إنشاء مثيل واحد من مدير النسخ الاحتياطية
backup_manager = BackupManager()
