@echo off
chcp 65001 > nul
title الوعد الصادق 4 - نظام إدارة المبيعات والتقسيط

echo ================================================
echo 🚀 مرحباً بك في برنامج الوعد الصادق 4
echo ================================================
echo.

echo 🔍 جاري التحقق من Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo.
    echo 📥 يرجى تحميل وتثبيت Python من:
    echo https://www.python.org/downloads/
    echo.
    echo ⚠️ تأكد من تفعيل خيار "Add Python to PATH" أثناء التثبيت
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

echo 🔍 جاري التحقق من المتطلبات...
python -c "import sqlite3" > nul 2>&1
if errorlevel 1 (
    echo ❌ SQLite غير متوفر
    pause
    exit /b 1
)

echo ✅ SQLite متوفر
echo.

echo 🚀 جاري تشغيل البرنامج...
echo.
echo 📋 بيانات تسجيل الدخول الافتراضية:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.
echo ⚠️ يُنصح بتغيير كلمة المرور فور تسجيل الدخول الأول
echo.

REM محاولة تشغيل الواجهة الرسومية أولاً
python -c "import PyQt5" > nul 2>&1
if errorlevel 1 (
    echo ⚠️ PyQt5 غير متوفر، سيتم تشغيل نسخة سطر الأوامر
    echo.
    echo 📦 لتثبيت الواجهة الرسومية، شغل:
    echo python -m pip install PyQt5
    echo.
    python console_app.py
) else (
    echo ✅ جاري تشغيل الواجهة الرسومية...
    python main.py
)

echo.
echo 👋 تم إغلاق البرنامج
pause
