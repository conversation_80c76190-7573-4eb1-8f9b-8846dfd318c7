# -*- coding: utf-8 -*-
"""
ملف إعداد التوزيع لبرنامج الوعد الصادق 4
"""

from setuptools import setup, find_packages
import os
import sys

# قراءة ملف README
def read_readme():
    try:
        with open("README.md", "r", encoding="utf-8") as f:
            return f.read()
    except:
        return "برنامج الوعد الصادق 4 - نظام إدارة المبيعات والتقسيط"

# قراءة المتطلبات
def read_requirements():
    try:
        with open("requirements.txt", "r", encoding="utf-8") as f:
            return [line.strip() for line in f if line.strip() and not line.startswith("#")]
    except:
        return [
            "PyQt5>=5.15.0",
            "reportlab>=3.6.0",
            "bcrypt>=3.2.0",
            "python-dateutil>=2.8.0",
            "openpyxl>=3.0.0",
            "Pillow>=8.0.0",
            "arabic-reshaper>=2.1.0",
            "python-bidi>=0.4.0"
        ]

# معلومات البرنامج
APP_NAME = "الوعد الصادق 4"
APP_VERSION = "4.0.0"
APP_DESCRIPTION = "نظام إدارة المبيعات والتقسيط"
APP_AUTHOR = "فريق الوعد الصادق"
APP_EMAIL = "<EMAIL>"
APP_URL = "https://www.alwaad.com"

setup(
    name="alwaad-alsadiq",
    version=APP_VERSION,
    description=APP_DESCRIPTION,
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author=APP_AUTHOR,
    author_email=APP_EMAIL,
    url=APP_URL,
    
    # الحزم والملفات
    packages=find_packages(),
    include_package_data=True,
    
    # المتطلبات
    install_requires=read_requirements(),
    python_requires=">=3.6",
    
    # ملفات البيانات
    package_data={
        "": ["*.md", "*.txt", "*.bat", "*.ico", "*.png", "*.jpg"],
        "config": ["*.py"],
        "database": ["*.py"],
        "models": ["*.py"],
        "ui": ["*.py"],
        "utils": ["*.py"],
        "resources": ["*"],
    },
    
    # نقاط الدخول
    entry_points={
        "console_scripts": [
            "alwaad-alsadiq=main:main",
            "alwaad-console=console_app:main",
        ],
        "gui_scripts": [
            "الوعد-الصادق-4=main:main",
        ]
    },
    
    # التصنيفات
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: End Users/Desktop",
        "Topic :: Office/Business :: Financial :: Point-Of-Sale",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.6",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: Microsoft :: Windows",
        "Natural Language :: Arabic",
    ],
    
    # كلمات مفتاحية
    keywords="sales management installments arabic pos retail",
    
    # معلومات إضافية
    project_urls={
        "Bug Reports": f"{APP_URL}/issues",
        "Source": f"{APP_URL}/source",
        "Documentation": f"{APP_URL}/docs",
    },
    
    # الترخيص
    license="MIT",
    
    # ملفات إضافية
    data_files=[
        ("", ["README.md", "requirements.txt"]),
        ("docs", ["INSTALLATION_GUIDE.md", "PROJECT_SUMMARY.md"]),
    ],
    
    # خيارات التثبيت
    zip_safe=False,
)
