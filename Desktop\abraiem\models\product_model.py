# -*- coding: utf-8 -*-
"""
نموذج المنتجات لبرنامج الوعد الصادق 4
"""

from database.database_manager import db_manager

class ProductModel:
    def __init__(self):
        self.db = db_manager
    
    def create_product(self, name, description, price, cost_price=None, category=None, 
                      stock_quantity=0, min_stock_level=0):
        """إنشاء منتج جديد"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO products (name, description, price, cost_price, category, 
                                    stock_quantity, min_stock_level)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (name, description, price, cost_price, category, stock_quantity, min_stock_level))
            
            product_id = cursor.lastrowid
            conn.commit()
            conn.close()
            return product_id
        except Exception as e:
            conn.close()
            raise Exception(f"فشل في إنشاء المنتج: {str(e)}")
    
    def get_all_products(self, active_only=True):
        """الحصول على جميع المنتجات"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        query = '''
            SELECT id, name, description, price, cost_price, category, 
                   stock_quantity, min_stock_level, is_active, created_at, updated_at
            FROM products
        '''
        
        if active_only:
            query += " WHERE is_active = 1"
        
        query += " ORDER BY name"
        
        cursor.execute(query)
        products = cursor.fetchall()
        conn.close()
        return products
    
    def get_product_by_id(self, product_id):
        """الحصول على منتج بواسطة المعرف"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, name, description, price, cost_price, category, 
                   stock_quantity, min_stock_level, is_active, created_at, updated_at
            FROM products 
            WHERE id = ?
        ''', (product_id,))
        
        product = cursor.fetchone()
        conn.close()
        return product
    
    def update_product(self, product_id, name=None, description=None, price=None, 
                      cost_price=None, category=None, stock_quantity=None, 
                      min_stock_level=None, is_active=None):
        """تحديث بيانات المنتج"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        updates = []
        params = []
        
        if name is not None:
            updates.append("name = ?")
            params.append(name)
        if description is not None:
            updates.append("description = ?")
            params.append(description)
        if price is not None:
            updates.append("price = ?")
            params.append(price)
        if cost_price is not None:
            updates.append("cost_price = ?")
            params.append(cost_price)
        if category is not None:
            updates.append("category = ?")
            params.append(category)
        if stock_quantity is not None:
            updates.append("stock_quantity = ?")
            params.append(stock_quantity)
        if min_stock_level is not None:
            updates.append("min_stock_level = ?")
            params.append(min_stock_level)
        if is_active is not None:
            updates.append("is_active = ?")
            params.append(is_active)
        
        if updates:
            updates.append("updated_at = CURRENT_TIMESTAMP")
            params.append(product_id)
            
            query = f"UPDATE products SET {', '.join(updates)} WHERE id = ?"
            cursor.execute(query, params)
            conn.commit()
        
        conn.close()
    
    def delete_product(self, product_id):
        """حذف المنتج (تعطيل فقط)"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE products 
            SET is_active = 0, updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        ''', (product_id,))
        
        conn.commit()
        conn.close()
    
    def search_products(self, search_term):
        """البحث في المنتجات"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, name, description, price, cost_price, category, 
                   stock_quantity, min_stock_level, is_active
            FROM products 
            WHERE is_active = 1 AND (
                name LIKE ? OR 
                description LIKE ? OR 
                category LIKE ?
            )
            ORDER BY name
        ''', (f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"))
        
        products = cursor.fetchall()
        conn.close()
        return products
    
    def update_stock(self, product_id, quantity_change):
        """تحديث المخزون"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE products 
            SET stock_quantity = stock_quantity + ?, 
                updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        ''', (quantity_change, product_id))
        
        conn.commit()
        conn.close()
    
    def get_low_stock_products(self):
        """الحصول على المنتجات منخفضة المخزون"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, name, stock_quantity, min_stock_level
            FROM products 
            WHERE is_active = 1 AND stock_quantity <= min_stock_level
            ORDER BY stock_quantity
        ''')
        
        products = cursor.fetchall()
        conn.close()
        return products
    
    def get_categories(self):
        """الحصول على جميع الفئات"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT DISTINCT category
            FROM products 
            WHERE is_active = 1 AND category IS NOT NULL AND category != ''
            ORDER BY category
        ''')
        
        categories = [row[0] for row in cursor.fetchall()]
        conn.close()
        return categories
