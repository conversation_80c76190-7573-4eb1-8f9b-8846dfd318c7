# -*- coding: utf-8 -*-
"""
مولد ملفات PDF لبرنامج الوعد الصادق 4
"""

import os
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib import colors
from reportlab.lib.enums import TA_RIGHT, TA_CENTER
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import arabic_reshaper
from bidi.algorithm import get_display
from config.settings import COMPANY_INFO, REPORTS_DIR

class PDFGenerator:
    def __init__(self):
        self.setup_arabic_font()
        
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            # يمكن إضافة خط عربي مخصص هنا
            # pdfmetrics.registerFont(TTFont('Arabic', 'path/to/arabic/font.ttf'))
            pass
        except:
            pass
    
    def format_arabic_text(self, text):
        """تنسيق النص العربي للعرض الصحيح في PDF"""
        if not text:
            return ""
        reshaped_text = arabic_reshaper.reshape(str(text))
        return get_display(reshaped_text)
    
    def generate_invoice(self, sale_data, customer_data, items_data, output_path=None):
        """إنشاء فاتورة PDF"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"invoice_{sale_data['id']}_{timestamp}.pdf"
            output_path = REPORTS_DIR / filename
        
        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        os.makedirs(REPORTS_DIR, exist_ok=True)
        
        doc = SimpleDocTemplate(str(output_path), pagesize=A4,
                              rightMargin=72, leftMargin=72,
                              topMargin=72, bottomMargin=18)
        
        # إعداد الأنماط
        styles = getSampleStyleSheet()
        
        # نمط للنص العربي
        arabic_style = ParagraphStyle(
            'Arabic',
            parent=styles['Normal'],
            fontName='Helvetica',
            fontSize=12,
            alignment=TA_RIGHT,
            rightIndent=0,
            leftIndent=0
        )
        
        # نمط للعناوين
        title_style = ParagraphStyle(
            'ArabicTitle',
            parent=styles['Title'],
            fontName='Helvetica-Bold',
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=20
        )
        
        # بناء محتوى الفاتورة
        story = []
        
        # رأس الفاتورة
        story.append(Paragraph(self.format_arabic_text(COMPANY_INFO['name']), title_style))
        story.append(Paragraph(self.format_arabic_text(COMPANY_INFO['address']), arabic_style))
        story.append(Paragraph(self.format_arabic_text(f"هاتف: {COMPANY_INFO['phone']}"), arabic_style))
        story.append(Spacer(1, 20))
        
        # معلومات الفاتورة
        invoice_info = [
            [self.format_arabic_text("رقم الفاتورة:"), str(sale_data['id'])],
            [self.format_arabic_text("تاريخ الفاتورة:"), sale_data['sale_date'][:10]],
            [self.format_arabic_text("نوع البيع:"), self.format_arabic_text("نقدي" if sale_data['sale_type'] == 'cash' else "تقسيط")]
        ]
        
        invoice_table = Table(invoice_info, colWidths=[2*inch, 2*inch])
        invoice_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(invoice_table)
        story.append(Spacer(1, 20))
        
        # معلومات العميل
        if customer_data:
            story.append(Paragraph(self.format_arabic_text("بيانات العميل"), title_style))
            customer_info = [
                [self.format_arabic_text("اسم العميل:"), self.format_arabic_text(customer_data['name'])],
                [self.format_arabic_text("رقم الهاتف:"), customer_data['phone'] or ""],
                [self.format_arabic_text("العنوان:"), self.format_arabic_text(customer_data['address'] or "")]
            ]
            
            customer_table = Table(customer_info, colWidths=[2*inch, 3*inch])
            customer_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(customer_table)
            story.append(Spacer(1, 20))
        
        # جدول المنتجات
        story.append(Paragraph(self.format_arabic_text("تفاصيل المنتجات"), title_style))
        
        # رأس الجدول
        table_data = [
            [self.format_arabic_text("المجموع"), self.format_arabic_text("السعر"), 
             self.format_arabic_text("الكمية"), self.format_arabic_text("المنتج")]
        ]
        
        # إضافة المنتجات
        for item in items_data:
            table_data.append([
                f"{item['total_price']:.2f}",
                f"{item['unit_price']:.2f}",
                str(item['quantity']),
                self.format_arabic_text(item['product_name'])
            ])
        
        # إضافة المجاميع
        table_data.append([
            f"{sale_data['total_amount']:.2f}",
            "",
            "",
            self.format_arabic_text("المجموع الفرعي")
        ])
        
        if sale_data['discount_amount'] > 0:
            table_data.append([
                f"{sale_data['discount_amount']:.2f}",
                "",
                "",
                self.format_arabic_text("الخصم")
            ])
        
        table_data.append([
            f"{sale_data['final_amount']:.2f}",
            "",
            "",
            self.format_arabic_text("المجموع النهائي")
        ])
        
        # إنشاء الجدول
        products_table = Table(table_data, colWidths=[1.5*inch, 1.5*inch, 1*inch, 3*inch])
        products_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('BACKGROUND', (0, -3), (-1, -1), colors.lightgrey),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold')
        ]))
        
        story.append(products_table)
        story.append(Spacer(1, 20))
        
        # معلومات التقسيط (إذا كان البيع بالتقسيط)
        if sale_data['sale_type'] == 'installment':
            story.append(Paragraph(self.format_arabic_text("تفاصيل التقسيط"), title_style))
            
            installment_info = [
                [self.format_arabic_text("الدفعة الأولى:"), f"{sale_data['down_payment']:.2f}"],
                [self.format_arabic_text("المبلغ المتبقي:"), f"{sale_data['remaining_amount']:.2f}"],
                [self.format_arabic_text("عدد الأقساط:"), str(sale_data['installment_count'])],
                [self.format_arabic_text("نسبة الفائدة:"), f"{sale_data['interest_rate']:.2f}%"],
                [self.format_arabic_text("القسط الشهري:"), f"{sale_data['monthly_payment']:.2f}"]
            ]
            
            installment_table = Table(installment_info, colWidths=[2*inch, 2*inch])
            installment_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(installment_table)
            story.append(Spacer(1, 20))
        
        # ذيل الفاتورة
        story.append(Spacer(1, 30))
        story.append(Paragraph(self.format_arabic_text("شكراً لتعاملكم معنا"), arabic_style))
        story.append(Paragraph(self.format_arabic_text(f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M')}"), arabic_style))
        
        # بناء PDF
        doc.build(story)
        
        return str(output_path)
    
    def generate_installment_schedule(self, sale_data, customer_data, installments_data, output_path=None):
        """إنشاء جدول الأقساط PDF"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"installment_schedule_{sale_data['id']}_{timestamp}.pdf"
            output_path = REPORTS_DIR / filename
        
        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        os.makedirs(REPORTS_DIR, exist_ok=True)
        
        doc = SimpleDocTemplate(str(output_path), pagesize=A4)
        
        # إعداد الأنماط
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle('ArabicTitle', parent=styles['Title'], 
                                   fontSize=18, alignment=TA_CENTER)
        
        story = []
        
        # رأس التقرير
        story.append(Paragraph(self.format_arabic_text("جدول الأقساط"), title_style))
        story.append(Spacer(1, 20))
        
        # معلومات العميل والبيع
        info_data = [
            [self.format_arabic_text("اسم العميل:"), self.format_arabic_text(customer_data['name'])],
            [self.format_arabic_text("رقم البيع:"), str(sale_data['id'])],
            [self.format_arabic_text("تاريخ البيع:"), sale_data['sale_date'][:10]],
            [self.format_arabic_text("المبلغ الإجمالي:"), f"{sale_data['final_amount']:.2f}"],
            [self.format_arabic_text("الدفعة الأولى:"), f"{sale_data['down_payment']:.2f}"],
            [self.format_arabic_text("المبلغ المتبقي:"), f"{sale_data['remaining_amount']:.2f}"]
        ]
        
        info_table = Table(info_data, colWidths=[2*inch, 3*inch])
        info_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(info_table)
        story.append(Spacer(1, 30))
        
        # جدول الأقساط
        table_data = [
            [self.format_arabic_text("الحالة"), self.format_arabic_text("المبلغ المدفوع"), 
             self.format_arabic_text("المبلغ المطلوب"), self.format_arabic_text("تاريخ الاستحقاق"), 
             self.format_arabic_text("رقم القسط")]
        ]
        
        for installment in installments_data:
            status_text = {
                'pending': 'معلق',
                'paid': 'مدفوع',
                'overdue': 'متأخر'
            }.get(installment['status'], installment['status'])
            
            table_data.append([
                self.format_arabic_text(status_text),
                f"{installment['paid_amount']:.2f}",
                f"{installment['amount']:.2f}",
                installment['due_date'],
                str(installment['installment_number'])
            ])
        
        installments_table = Table(table_data, colWidths=[1*inch, 1.2*inch, 1.2*inch, 1.5*inch, 1*inch])
        installments_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold')
        ]))
        
        story.append(installments_table)
        
        # بناء PDF
        doc.build(story)
        
        return str(output_path)
