# -*- coding: utf-8 -*-
"""
إعدادات برنامج الوعد الصادق 4
"""

import os
from pathlib import Path

# مسارات البرنامج
BASE_DIR = Path(__file__).parent.parent
DATABASE_PATH = BASE_DIR / "data" / "alwaad_alsadiq.db"
BACKUP_DIR = BASE_DIR / "backups"
REPORTS_DIR = BASE_DIR / "reports"
RESOURCES_DIR = BASE_DIR / "resources"

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    'path': str(DATABASE_PATH),
    'backup_interval': 24,  # ساعات
    'auto_backup': True
}

# إعدادات الواجهة
UI_CONFIG = {
    'theme': 'modern',
    'language': 'ar',
    'font_family': 'Segoe UI',
    'font_size': 10,
    'rtl_support': True
}

# إعدادات الشركة
COMPANY_INFO = {
    'name': 'الوعد الصادق 4',
    'address': 'العنوان الخاص بالشركة',
    'phone': '+966 XX XXX XXXX',
    'email': '<EMAIL>',
    'logo_path': str(RESOURCES_DIR / "logo.png")
}

# إعدادات الأمان
SECURITY_CONFIG = {
    'password_min_length': 6,
    'session_timeout': 480,  # دقائق (8 ساعات)
    'max_login_attempts': 3
}

# إعدادات التقسيط
INSTALLMENT_CONFIG = {
    'default_interest_rate': 0.0,  # نسبة الفائدة الافتراضية
    'min_down_payment_percent': 10,  # الحد الأدنى للدفعة الأولى
    'max_installments': 60,  # الحد الأقصى لعدد الأقساط
    'reminder_days_before': 3  # تذكير قبل كم يوم من موعد القسط
}

# إنشاء المجلدات المطلوبة
def create_directories():
    """إنشاء المجلدات المطلوبة للبرنامج"""
    directories = [
        BASE_DIR / "data",
        BACKUP_DIR,
        REPORTS_DIR,
        RESOURCES_DIR
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

if __name__ == "__main__":
    create_directories()
