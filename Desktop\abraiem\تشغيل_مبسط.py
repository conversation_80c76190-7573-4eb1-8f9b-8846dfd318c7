#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الوعد الصادق 4 - نسخة مبسطة تعمل بدون مكتبات خارجية
"""

import sqlite3
import os
import sys
from datetime import datetime, timedelta
import hashlib

class AlwaadSimple:
    def __init__(self):
        self.db_path = "data/alwaad_simple.db"
        self.current_user = None
        self.setup_database()
        
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        os.makedirs("data", exist_ok=True)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY,
                username TEXT UNIQUE,
                password TEXT,
                full_name TEXT,
                role TEXT DEFAULT 'admin'
            )
        ''')
        
        # جدول المنتجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                price REAL NOT NULL,
                stock INTEGER DEFAULT 0,
                category TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول العملاء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المبيعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales (
                id INTEGER PRIMARY KEY,
                customer_id INTEGER,
                product_id INTEGER,
                quantity INTEGER,
                unit_price REAL,
                total_amount REAL,
                sale_type TEXT DEFAULT 'cash',
                sale_date TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        # إضافة بيانات تجريبية
        self.add_sample_data(cursor)
        
        conn.commit()
        conn.close()
        
    def add_sample_data(self, cursor):
        """إضافة بيانات تجريبية"""
        # مستخدم افتراضي
        cursor.execute('''
            INSERT OR REPLACE INTO users (id, username, password, full_name, role)
            VALUES (1, 'admin', 'admin123', 'المدير العام', 'admin')
        ''')
        
        # منتجات تجريبية
        products = [
            (1, 'لابتوب HP Pavilion', 3500.0, 10, 'إلكترونيات'),
            (2, 'هاتف Samsung Galaxy', 1200.0, 25, 'إلكترونيات'),
            (3, 'طاولة مكتب خشبية', 800.0, 5, 'أثاث'),
            (4, 'كرسي مكتب مريح', 450.0, 8, 'أثاث'),
            (5, 'طابعة Canon', 650.0, 3, 'إلكترونيات'),
            (6, 'شاشة Dell 24 بوصة', 900.0, 7, 'إلكترونيات'),
            (7, 'لوحة مفاتيح لاسلكية', 150.0, 20, 'إكسسوارات'),
            (8, 'ماوس لاسلكي', 80.0, 30, 'إكسسوارات')
        ]
        
        for product in products:
            cursor.execute('''
                INSERT OR REPLACE INTO products (id, name, price, stock, category)
                VALUES (?, ?, ?, ?, ?)
            ''', product)
        
        # عملاء تجريبيون
        customers = [
            (1, 'أحمد محمد العلي', '0501234567', 'الرياض - حي النخيل'),
            (2, 'فاطمة علي أحمد', '0509876543', 'جدة - حي الصفا'),
            (3, 'محمد سعد الغامدي', '0551122334', 'الدمام - حي الفيصلية'),
            (4, 'نورا أحمد السعد', '0544556677', 'الرياض - حي العليا'),
            (5, 'سارة خالد المطيري', '0555667788', 'مكة - حي العزيزية'),
            (6, 'عبدالله يوسف', '0566778899', 'المدينة - حي قباء'),
            (7, 'مريم عبدالرحمن', '0577889900', 'الطائف - حي الشهداء')
        ]
        
        for customer in customers:
            cursor.execute('''
                INSERT OR REPLACE INTO customers (id, name, phone, address)
                VALUES (?, ?, ?, ?)
            ''', customer)
        
        # مبيعات تجريبية
        sales = [
            (1, 1, 1, 1, 3500.0, 3500.0, 'تقسيط'),
            (2, 2, 2, 2, 1200.0, 2400.0, 'نقدي'),
            (3, 3, 3, 1, 800.0, 800.0, 'نقدي'),
            (4, 1, 4, 2, 450.0, 900.0, 'تقسيط'),
            (5, 4, 5, 1, 650.0, 650.0, 'نقدي'),
            (6, 5, 6, 1, 900.0, 900.0, 'تقسيط')
        ]
        
        for sale in sales:
            cursor.execute('''
                INSERT OR REPLACE INTO sales (id, customer_id, product_id, quantity, unit_price, total_amount, sale_type)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', sale)
    
    def login(self, username, password):
        """تسجيل الدخول"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, full_name, role
            FROM users 
            WHERE username = ? AND password = ?
        ''', (username, password))
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            self.current_user = {
                'id': user[0],
                'username': user[1],
                'full_name': user[2],
                'role': user[3]
            }
            return True
        return False
    
    def show_products(self):
        """عرض المنتجات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM products ORDER BY category, name')
        products = cursor.fetchall()
        conn.close()
        
        print("\n" + "="*80)
        print("📦 قائمة المنتجات")
        print("="*80)
        print(f"{'الرقم':<5} {'الاسم':<25} {'السعر':<10} {'المخزون':<10} {'الفئة':<15}")
        print("-"*80)
        
        total_value = 0
        for product in products:
            print(f"{product[0]:<5} {product[1]:<25} {product[2]:<10.2f} {product[3]:<10} {product[4] or 'غير محدد':<15}")
            total_value += product[2] * product[3]
        
        print("-"*80)
        print(f"إجمالي المنتجات: {len(products)}")
        print(f"إجمالي قيمة المخزون: {total_value:,.2f} ريال")
    
    def show_customers(self):
        """عرض العملاء"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM customers ORDER BY name')
        customers = cursor.fetchall()
        conn.close()
        
        print("\n" + "="*80)
        print("👥 قائمة العملاء")
        print("="*80)
        print(f"{'الرقم':<5} {'الاسم':<25} {'الهاتف':<15} {'العنوان':<30}")
        print("-"*80)
        
        for customer in customers:
            print(f"{customer[0]:<5} {customer[1]:<25} {customer[2] or 'غير محدد':<15} {customer[3] or 'غير محدد':<30}")
        
        print("-"*80)
        print(f"إجمالي العملاء: {len(customers)}")
    
    def show_sales(self):
        """عرض المبيعات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT s.id, c.name, p.name, s.quantity, s.total_amount, s.sale_type, s.sale_date
            FROM sales s
            LEFT JOIN customers c ON s.customer_id = c.id
            JOIN products p ON s.product_id = p.id
            ORDER BY s.sale_date DESC
        ''')
        sales = cursor.fetchall()
        conn.close()
        
        print("\n" + "="*100)
        print("💰 قائمة المبيعات")
        print("="*100)
        print(f"{'الرقم':<5} {'العميل':<20} {'المنتج':<20} {'الكمية':<8} {'المبلغ':<10} {'النوع':<10} {'التاريخ':<15}")
        print("-"*100)
        
        total_sales = 0
        cash_sales = 0
        installment_sales = 0
        
        for sale in sales:
            customer_name = sale[1] or "عميل مباشر"
            sale_date = sale[6][:10] if sale[6] else "غير محدد"
            print(f"{sale[0]:<5} {customer_name:<20} {sale[2]:<20} {sale[3]:<8} {sale[4]:<10.2f} {sale[5]:<10} {sale_date:<15}")
            total_sales += sale[4]
            if sale[5] == 'نقدي':
                cash_sales += sale[4]
            else:
                installment_sales += sale[4]
        
        print("-"*100)
        print(f"إجمالي المبيعات: {len(sales)} عملية")
        print(f"إجمالي المبلغ: {total_sales:,.2f} ريال")
        print(f"المبيعات النقدية: {cash_sales:,.2f} ريال")
        print(f"المبيعات بالتقسيط: {installment_sales:,.2f} ريال")
    
    def show_statistics(self):
        """عرض الإحصائيات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # إحصائيات عامة
        cursor.execute('SELECT COUNT(*) FROM products')
        products_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM customers')
        customers_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM sales')
        sales_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT SUM(total_amount) FROM sales')
        total_revenue = cursor.fetchone()[0] or 0
        
        cursor.execute('SELECT COUNT(*) FROM sales WHERE sale_type = "تقسيط"')
        installment_sales = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM sales WHERE sale_type = "نقدي"')
        cash_sales = cursor.fetchone()[0]
        
        # أفضل المنتجات مبيعاً
        cursor.execute('''
            SELECT p.name, SUM(s.quantity) as total_sold, SUM(s.total_amount) as total_revenue
            FROM sales s
            JOIN products p ON s.product_id = p.id
            GROUP BY p.id, p.name
            ORDER BY total_sold DESC
            LIMIT 3
        ''')
        top_products = cursor.fetchall()
        
        conn.close()
        
        print("\n" + "="*60)
        print("📊 إحصائيات البرنامج")
        print("="*60)
        print(f"📦 عدد المنتجات: {products_count}")
        print(f"👥 عدد العملاء: {customers_count}")
        print(f"💰 عدد المبيعات: {sales_count}")
        print(f"💵 إجمالي الإيرادات: {total_revenue:,.2f} ريال")
        print(f"📅 المبيعات بالتقسيط: {installment_sales}")
        print(f"💸 المبيعات النقدية: {cash_sales}")
        
        if top_products:
            print("\n🏆 أفضل المنتجات مبيعاً:")
            print("-" * 50)
            for i, product in enumerate(top_products, 1):
                print(f"{i}. {product[0]} - {product[1]} قطعة - {product[2]:,.2f} ريال")
        
        print("="*60)
    
    def add_product_interactive(self):
        """إضافة منتج تفاعلي"""
        print("\n➕ إضافة منتج جديد")
        print("-" * 30)
        
        try:
            name = input("اسم المنتج: ").strip()
            if not name:
                print("❌ اسم المنتج مطلوب")
                return
            
            price = float(input("سعر المنتج: "))
            stock = int(input("كمية المخزون: "))
            category = input("فئة المنتج (اختياري): ").strip() or None
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO products (name, price, stock, category)
                VALUES (?, ?, ?, ?)
            ''', (name, price, stock, category))
            
            product_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            print(f"✅ تم إضافة المنتج بنجاح برقم: {product_id}")
            
        except ValueError:
            print("❌ خطأ في إدخال البيانات")
        except Exception as e:
            print(f"❌ خطأ: {e}")
    
    def search_products(self):
        """البحث في المنتجات"""
        search_term = input("\n🔍 أدخل كلمة البحث: ").strip()
        
        if not search_term:
            print("❌ يرجى إدخال كلمة البحث")
            return
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM products 
            WHERE name LIKE ? OR category LIKE ?
            ORDER BY name
        ''', (f"%{search_term}%", f"%{search_term}%"))
        
        products = cursor.fetchall()
        conn.close()
        
        if not products:
            print("🔍 لم يتم العثور على منتجات")
            return
        
        print(f"\n🔍 نتائج البحث ({len(products)} منتج)")
        print("-" * 60)
        print(f"{'الرقم':<5} {'الاسم':<25} {'السعر':<10} {'المخزون':<10}")
        print("-" * 60)
        
        for product in products:
            print(f"{product[0]:<5} {product[1]:<25} {product[2]:<10.2f} {product[3]:<10}")
    
    def main_menu(self):
        """القائمة الرئيسية"""
        while True:
            print("\n" + "="*60)
            print("📋 القائمة الرئيسية - الوعد الصادق 4")
            print("="*60)
            print("1. 📦 عرض المنتجات")
            print("2. 👥 عرض العملاء")
            print("3. 💰 عرض المبيعات")
            print("4. 📊 عرض الإحصائيات")
            print("5. ➕ إضافة منتج جديد")
            print("6. 🔍 البحث في المنتجات")
            print("7. 💾 إنشاء نسخة احتياطية")
            print("0. 🚪 تسجيل الخروج")
            print("="*60)
            
            choice = input("اختر رقم العملية: ").strip()
            
            if choice == "1":
                self.show_products()
            elif choice == "2":
                self.show_customers()
            elif choice == "3":
                self.show_sales()
            elif choice == "4":
                self.show_statistics()
            elif choice == "5":
                self.add_product_interactive()
            elif choice == "6":
                self.search_products()
            elif choice == "7":
                self.create_backup()
            elif choice == "0":
                print("👋 تم تسجيل الخروج بنجاح")
                break
            else:
                print("❌ اختيار غير صحيح")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            os.makedirs("backups", exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{timestamp}.db"
            backup_path = f"backups/{backup_name}"
            
            import shutil
            shutil.copy2(self.db_path, backup_path)
            
            file_size = os.path.getsize(backup_path)
            
            print(f"\n💾 تم إنشاء النسخة الاحتياطية بنجاح!")
            print(f"📁 اسم الملف: {backup_name}")
            print(f"📂 المسار: {backup_path}")
            print(f"📊 الحجم: {file_size} بايت")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
    
    def run(self):
        """تشغيل البرنامج"""
        print("🚀 مرحباً بك في برنامج الوعد الصادق 4")
        print("نسخة مبسطة - تعمل بدون مكتبات خارجية")
        print("=" * 60)
        
        # تسجيل الدخول التلقائي للتجربة
        if self.login("admin", "admin123"):
            print(f"✅ مرحباً {self.current_user['full_name']}!")
            print(f"الصلاحية: {self.current_user['role']}")
            self.main_menu()
        else:
            print("❌ فشل في تسجيل الدخول")

def main():
    """تشغيل البرنامج"""
    try:
        app = AlwaadSimple()
        app.run()
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف البرنامج")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
