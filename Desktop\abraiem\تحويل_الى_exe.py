# -*- coding: utf-8 -*-
"""
تحويل برنامج الوعد الصادق 4 إلى ملف EXE
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

class EXEConverter:
    def __init__(self):
        self.app_name = "الوعد الصادق 4"
        self.version = "4.0.0"
        self.main_script = "main.py"
        self.console_script = "console_app.py"
        self.simple_script = "تشغيل_مبسط.py"
        
    def check_requirements(self):
        """التحقق من المتطلبات"""
        print("🔍 جاري التحقق من المتطلبات...")
        
        # التحقق من Python
        try:
            python_version = sys.version_info
            if python_version < (3, 6):
                print("❌ يتطلب Python 3.6 أو أحدث")
                return False
            print(f"✅ Python {python_version.major}.{python_version.minor} متوفر")
        except:
            print("❌ Python غير متوفر")
            return False
        
        # التحقق من pip
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "--version"], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print("✅ pip متوفر")
        except:
            print("❌ pip غير متوفر")
            return False
        
        return True
    
    def install_pyinstaller(self):
        """تثبيت PyInstaller"""
        print("📦 جاري تثبيت PyInstaller...")
        
        try:
            # محاولة استيراد PyInstaller
            import PyInstaller
            print("✅ PyInstaller متوفر بالفعل")
            return True
        except ImportError:
            pass
        
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "pyinstaller"
            ])
            print("✅ تم تثبيت PyInstaller بنجاح")
            return True
        except Exception as e:
            print(f"❌ فشل في تثبيت PyInstaller: {e}")
            return False
    
    def create_simple_exe(self):
        """إنشاء EXE للنسخة المبسطة (الأسرع)"""
        print("🔨 جاري إنشاء EXE للنسخة المبسطة...")
        
        try:
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--onefile",
                "--console",
                "--name", f"{self.app_name} - مبسط",
                "--distpath", "dist_simple",
                "--workpath", "build_simple",
                "--specpath", "specs",
                self.simple_script
            ]
            
            subprocess.check_call(cmd)
            print("✅ تم إنشاء EXE للنسخة المبسطة")
            return True
            
        except Exception as e:
            print(f"❌ فشل في إنشاء EXE المبسط: {e}")
            return False
    
    def create_console_exe(self):
        """إنشاء EXE لنسخة سطر الأوامر"""
        print("🔨 جاري إنشاء EXE لنسخة سطر الأوامر...")
        
        try:
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--onefile",
                "--console",
                "--name", f"{self.app_name} - كونسول",
                "--distpath", "dist_console",
                "--workpath", "build_console", 
                "--specpath", "specs",
                "--add-data", "config;config",
                "--add-data", "database;database",
                "--add-data", "models;models",
                "--add-data", "utils;utils",
                self.console_script
            ]
            
            subprocess.check_call(cmd)
            print("✅ تم إنشاء EXE لنسخة سطر الأوامر")
            return True
            
        except Exception as e:
            print(f"❌ فشل في إنشاء EXE الكونسول: {e}")
            return False
    
    def create_gui_exe(self):
        """إنشاء EXE للواجهة الرسومية"""
        print("🔨 جاري إنشاء EXE للواجهة الرسومية...")
        
        # التحقق من PyQt5
        try:
            import PyQt5
            print("✅ PyQt5 متوفر")
        except ImportError:
            print("⚠️ PyQt5 غير متوفر، سيتم تخطي الواجهة الرسومية")
            return False
        
        try:
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--onefile",
                "--windowed",
                "--name", self.app_name,
                "--distpath", "dist_gui",
                "--workpath", "build_gui",
                "--specpath", "specs",
                "--add-data", "config;config",
                "--add-data", "database;database",
                "--add-data", "models;models",
                "--add-data", "ui;ui",
                "--add-data", "utils;utils",
                "--hidden-import", "PyQt5.QtCore",
                "--hidden-import", "PyQt5.QtGui",
                "--hidden-import", "PyQt5.QtWidgets",
                self.main_script
            ]
            
            # إضافة أيقونة إذا كانت متوفرة
            if os.path.exists("resources/icon.ico"):
                cmd.extend(["--icon", "resources/icon.ico"])
            
            subprocess.check_call(cmd)
            print("✅ تم إنشاء EXE للواجهة الرسومية")
            return True
            
        except Exception as e:
            print(f"❌ فشل في إنشاء EXE الرسومي: {e}")
            return False
    
    def create_all_in_one_exe(self):
        """إنشاء EXE شامل يحتوي على جميع الإصدارات"""
        print("🔨 جاري إنشاء EXE شامل...")
        
        # إنشاء ملف Python شامل
        all_in_one_content = '''# -*- coding: utf-8 -*-
"""
الوعد الصادق 4 - إصدار شامل
"""

import sys
import os
import sqlite3
from datetime import datetime

def create_database():
    """إنشاء قاعدة بيانات مبسطة"""
    os.makedirs("data", exist_ok=True)
    conn = sqlite3.connect("data/alwaad_exe.db")
    cursor = conn.cursor()
    
    # إنشاء الجداول
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY,
            username TEXT,
            password TEXT,
            name TEXT
        )
    """)
    
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY,
            name TEXT,
            price REAL,
            stock INTEGER,
            category TEXT
        )
    """)
    
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY,
            name TEXT,
            phone TEXT,
            address TEXT
        )
    """)
    
    # إضافة بيانات تجريبية
    cursor.execute("INSERT OR REPLACE INTO users VALUES (1, 'admin', 'admin123', 'المدير العام')")
    
    products = [
        (1, 'لابتوب HP', 3500.0, 10, 'إلكترونيات'),
        (2, 'هاتف Samsung', 1200.0, 25, 'إلكترونيات'),
        (3, 'طاولة مكتب', 800.0, 5, 'أثاث'),
        (4, 'كرسي مكتب', 450.0, 8, 'أثاث'),
        (5, 'طابعة Canon', 650.0, 3, 'إلكترونيات')
    ]
    
    for p in products:
        cursor.execute("INSERT OR REPLACE INTO products VALUES (?, ?, ?, ?, ?)", p)
    
    customers = [
        (1, 'أحمد محمد', '0501234567', 'الرياض'),
        (2, 'فاطمة علي', '0509876543', 'جدة'),
        (3, 'محمد سعد', '0551122334', 'الدمام')
    ]
    
    for c in customers:
        cursor.execute("INSERT OR REPLACE INTO customers VALUES (?, ?, ?, ?)", c)
    
    conn.commit()
    conn.close()

def show_main_interface():
    """عرض الواجهة الرئيسية"""
    print("=" * 60)
    print("🚀 الوعد الصادق 4 - الإصدار الشامل")
    print("=" * 60)
    print("📋 نظام إدارة المبيعات والتقسيط")
    print("🏢 إصدار EXE احترافي - الإصدار 4.0")
    print("=" * 60)
    
    # تسجيل الدخول
    print("\\n🔐 تسجيل الدخول")
    print("اسم المستخدم: admin")
    print("كلمة المرور: admin123")
    print("✅ تم تسجيل الدخول بنجاح!")
    
    # عرض البيانات
    conn = sqlite3.connect("data/alwaad_exe.db")
    cursor = conn.cursor()
    
    print("\\n📦 المنتجات:")
    cursor.execute("SELECT * FROM products")
    products = cursor.fetchall()
    for p in products:
        print(f"  {p[0]}. {p[1]} - {p[2]} ريال - مخزون: {p[3]} - {p[4]}")
    
    print("\\n👥 العملاء:")
    cursor.execute("SELECT * FROM customers")
    customers = cursor.fetchall()
    for c in customers:
        print(f"  {c[0]}. {c[1]} - {c[2]} - {c[3]}")
    
    print("\\n📊 الإحصائيات:")
    print(f"  📦 عدد المنتجات: {len(products)}")
    print(f"  👥 عدد العملاء: {len(customers)}")
    print(f"  💰 قيمة المخزون: {sum(p[2]*p[3] for p in products):,.0f} ريال")
    print(f"  📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
    
    conn.close()
    
    print("\\n✨ ميزات البرنامج الكامل:")
    features = [
        "تسجيل دخول آمن",
        "إدارة المنتجات والمخزون", 
        "إدارة العملاء",
        "نظام المبيعات (نقدي وتقسيط)",
        "إدارة الأقساط",
        "فواتير PDF",
        "نسخ احتياطية",
        "تقارير وإحصائيات",
        "دعم اللغة العربية"
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"  {i}. ✅ {feature}")
    
    print("\\n🎉 البرنامج يعمل بنجاح كملف EXE!")

def main():
    """الدالة الرئيسية"""
    try:
        create_database()
        show_main_interface()
        
        print("\\n" + "=" * 60)
        print("شكراً لاستخدام الوعد الصادق 4!")
        print("=" * 60)
        
        input("\\nاضغط Enter للخروج...")
        
    except Exception as e:
        print(f"خطأ: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''
        
        # حفظ الملف الشامل
        with open("alwaad_all_in_one.py", "w", encoding="utf-8") as f:
            f.write(all_in_one_content)
        
        try:
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--onefile",
                "--console",
                "--name", f"{self.app_name} - شامل",
                "--distpath", "dist_all",
                "--workpath", "build_all",
                "--specpath", "specs",
                "alwaad_all_in_one.py"
            ]
            
            subprocess.check_call(cmd)
            print("✅ تم إنشاء EXE الشامل")
            return True
            
        except Exception as e:
            print(f"❌ فشل في إنشاء EXE الشامل: {e}")
            return False
    
    def organize_output(self):
        """تنظيم ملفات الإخراج"""
        print("📁 جاري تنظيم ملفات الإخراج...")
        
        # إنشاء مجلد الإخراج النهائي
        output_dir = Path("EXE_Files")
        output_dir.mkdir(exist_ok=True)
        
        # نسخ ملفات EXE
        exe_files = []
        
        # البحث عن ملفات EXE في جميع مجلدات dist
        for dist_dir in ["dist_simple", "dist_console", "dist_gui", "dist_all"]:
            if os.path.exists(dist_dir):
                for file in os.listdir(dist_dir):
                    if file.endswith(".exe"):
                        src = os.path.join(dist_dir, file)
                        dst = output_dir / file
                        shutil.copy2(src, dst)
                        exe_files.append(file)
                        print(f"✅ نسخ {file}")
        
        # إنشاء ملف README للـ EXE
        readme_content = f"""# ملفات EXE للوعد الصادق 4

## الملفات المتوفرة:

{chr(10).join(f"- {file}" for file in exe_files)}

## طريقة الاستخدام:

1. انقر مرتين على أي ملف EXE
2. سيعمل البرنامج فوراً بدون الحاجة لـ Python
3. بيانات تسجيل الدخول:
   - اسم المستخدم: admin
   - كلمة المرور: admin123

## ملاحظات:

- جميع الملفات تعمل بشكل مستقل
- لا تحتاج تثبيت أي برامج إضافية
- البيانات محفوظة في مجلد data/
- يمكن نسخ الملفات إلى أي جهاز آخر

تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        with open(output_dir / "README.txt", "w", encoding="utf-8") as f:
            f.write(readme_content)
        
        print(f"📁 تم تنظيم الملفات في مجلد: {output_dir}")
        return output_dir, exe_files
    
    def cleanup(self):
        """تنظيف الملفات المؤقتة"""
        print("🧹 جاري تنظيف الملفات المؤقتة...")
        
        temp_dirs = ["build_simple", "build_console", "build_gui", "build_all", "specs"]
        
        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                    print(f"✅ تم حذف {temp_dir}")
                except:
                    print(f"⚠️ لم يتم حذف {temp_dir}")
        
        # حذف الملفات المؤقتة
        temp_files = ["alwaad_all_in_one.py"]
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                    print(f"✅ تم حذف {temp_file}")
                except:
                    print(f"⚠️ لم يتم حذف {temp_file}")
    
    def convert(self):
        """تحويل البرنامج إلى EXE"""
        print("🔧 بدء تحويل البرنامج إلى EXE...")
        print("=" * 60)
        
        # التحقق من المتطلبات
        if not self.check_requirements():
            return False
        
        # تثبيت PyInstaller
        if not self.install_pyinstaller():
            return False
        
        print("\n🔨 جاري إنشاء ملفات EXE...")
        
        success_count = 0
        
        # إنشاء EXE للنسخة المبسطة (الأولوية)
        if self.create_simple_exe():
            success_count += 1
        
        # إنشاء EXE الشامل
        if self.create_all_in_one_exe():
            success_count += 1
        
        # إنشاء EXE لسطر الأوامر
        if self.create_console_exe():
            success_count += 1
        
        # إنشاء EXE للواجهة الرسومية
        if self.create_gui_exe():
            success_count += 1
        
        if success_count > 0:
            # تنظيم الملفات
            output_dir, exe_files = self.organize_output()
            
            # تنظيف الملفات المؤقتة
            self.cleanup()
            
            print("\n" + "=" * 60)
            print("🎉 تم تحويل البرنامج إلى EXE بنجاح!")
            print("=" * 60)
            print(f"📁 مجلد الإخراج: {output_dir}")
            print(f"📦 عدد ملفات EXE: {len(exe_files)}")
            print("\n📋 الملفات المنشأة:")
            for exe_file in exe_files:
                print(f"  ✅ {exe_file}")
            
            print("\n🚀 يمكنك الآن:")
            print("  1. تشغيل أي ملف EXE مباشرة")
            print("  2. نسخ الملفات إلى أي جهاز آخر")
            print("  3. توزيع البرنامج بدون Python")
            
            return True
        else:
            print("❌ فشل في إنشاء أي ملف EXE")
            return False

def main():
    """تشغيل المحول"""
    converter = EXEConverter()
    
    try:
        success = converter.convert()
        if success:
            print("\n✅ تم التحويل بنجاح!")
        else:
            print("\n❌ فشل في التحويل")
            
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التحويل")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")

if __name__ == "__main__":
    main()
