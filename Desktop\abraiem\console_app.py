# -*- coding: utf-8 -*-
"""
نسخة سطر الأوامر من برنامج الوعد الصادق 4
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class ConsoleApp:
    def __init__(self):
        self.current_user = None
        self.setup_database()
        
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            from config.settings import create_directories
            create_directories()
            
            from database.database_manager import db_manager
            print("✅ تم إعداد قاعدة البيانات بنجاح")
            return True
        except Exception as e:
            print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
            return False
    
    def login(self):
        """تسجيل الدخول"""
        print("\n" + "="*50)
        print("🔐 تسجيل الدخول - الوعد الصادق 4")
        print("="*50)
        
        try:
            from models.user_model import UserModel
            user_model = UserModel()
            
            username = input("اسم المستخدم: ").strip()
            password = input("كلمة المرور: ").strip()
            
            user = user_model.authenticate_user(username, password)
            
            if user:
                self.current_user = user
                print(f"✅ مرحباً {user['full_name']}!")
                print(f"الصلاحية: {'مدير' if user['role'] == 'admin' else 'موظف مبيعات'}")
                return True
            else:
                print("❌ اسم المستخدم أو كلمة المرور غير صحيحة")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تسجيل الدخول: {e}")
            return False
    
    def show_main_menu(self):
        """عرض القائمة الرئيسية"""
        while True:
            print("\n" + "="*50)
            print("📋 القائمة الرئيسية")
            print("="*50)
            print("1. إدارة المنتجات")
            print("2. إدارة العملاء") 
            print("3. المبيعات")
            print("4. الأقساط")
            print("5. التقارير")
            if self.current_user['role'] == 'admin':
                print("6. إدارة المستخدمين")
                print("7. النسخ الاحتياطية")
            print("0. تسجيل الخروج")
            print("="*50)
            
            choice = input("اختر رقم العملية: ").strip()
            
            if choice == "1":
                self.products_menu()
            elif choice == "2":
                self.customers_menu()
            elif choice == "3":
                self.sales_menu()
            elif choice == "4":
                self.installments_menu()
            elif choice == "5":
                self.reports_menu()
            elif choice == "6" and self.current_user['role'] == 'admin':
                self.users_menu()
            elif choice == "7" and self.current_user['role'] == 'admin':
                self.backup_menu()
            elif choice == "0":
                print("👋 تم تسجيل الخروج بنجاح")
                break
            else:
                print("❌ اختيار غير صحيح")
    
    def products_menu(self):
        """قائمة إدارة المنتجات"""
        while True:
            print("\n📦 إدارة المنتجات")
            print("1. عرض جميع المنتجات")
            print("2. إضافة منتج جديد")
            print("3. البحث عن منتج")
            print("0. العودة للقائمة الرئيسية")
            
            choice = input("اختر العملية: ").strip()
            
            if choice == "1":
                self.list_products()
            elif choice == "2":
                self.add_product()
            elif choice == "3":
                self.search_products()
            elif choice == "0":
                break
            else:
                print("❌ اختيار غير صحيح")
    
    def list_products(self):
        """عرض جميع المنتجات"""
        try:
            from models.product_model import ProductModel
            product_model = ProductModel()
            
            products = product_model.get_all_products()
            
            if not products:
                print("📦 لا توجد منتجات")
                return
            
            print(f"\n📦 قائمة المنتجات ({len(products)} منتج)")
            print("-" * 80)
            print(f"{'الرقم':<5} {'الاسم':<20} {'السعر':<10} {'المخزون':<10} {'الفئة':<15}")
            print("-" * 80)
            
            for product in products:
                print(f"{product['id']:<5} {product['name']:<20} {product['price']:<10.2f} "
                      f"{product['stock_quantity']:<10} {product['category'] or 'غير محدد':<15}")
                      
        except Exception as e:
            print(f"❌ خطأ في عرض المنتجات: {e}")
    
    def add_product(self):
        """إضافة منتج جديد"""
        try:
            from models.product_model import ProductModel
            product_model = ProductModel()
            
            print("\n➕ إضافة منتج جديد")
            name = input("اسم المنتج: ").strip()
            description = input("وصف المنتج (اختياري): ").strip() or None
            
            while True:
                try:
                    price = float(input("سعر المنتج: "))
                    break
                except ValueError:
                    print("❌ يرجى إدخال رقم صحيح للسعر")
            
            while True:
                try:
                    stock = int(input("كمية المخزون: "))
                    break
                except ValueError:
                    print("❌ يرجى إدخال رقم صحيح للكمية")
            
            category = input("فئة المنتج (اختياري): ").strip() or None
            
            product_id = product_model.create_product(
                name=name,
                description=description,
                price=price,
                stock_quantity=stock,
                category=category
            )
            
            print(f"✅ تم إضافة المنتج بنجاح برقم: {product_id}")
            
        except Exception as e:
            print(f"❌ خطأ في إضافة المنتج: {e}")
    
    def search_products(self):
        """البحث عن منتج"""
        try:
            from models.product_model import ProductModel
            product_model = ProductModel()
            
            search_term = input("أدخل كلمة البحث: ").strip()
            
            if not search_term:
                print("❌ يرجى إدخال كلمة البحث")
                return
            
            products = product_model.search_products(search_term)
            
            if not products:
                print("🔍 لم يتم العثور على منتجات")
                return
            
            print(f"\n🔍 نتائج البحث ({len(products)} منتج)")
            print("-" * 80)
            print(f"{'الرقم':<5} {'الاسم':<20} {'السعر':<10} {'المخزون':<10} {'الفئة':<15}")
            print("-" * 80)
            
            for product in products:
                print(f"{product['id']:<5} {product['name']:<20} {product['price']:<10.2f} "
                      f"{product['stock_quantity']:<10} {product['category'] or 'غير محدد':<15}")
                      
        except Exception as e:
            print(f"❌ خطأ في البحث: {e}")
    
    def customers_menu(self):
        """قائمة إدارة العملاء"""
        print("\n👥 إدارة العملاء")
        print("هذه الميزة ستكون متوفرة قريباً...")
    
    def sales_menu(self):
        """قائمة المبيعات"""
        print("\n💰 المبيعات")
        print("هذه الميزة ستكون متوفرة قريباً...")
    
    def installments_menu(self):
        """قائمة الأقساط"""
        print("\n📅 إدارة الأقساط")
        print("هذه الميزة ستكون متوفرة قريباً...")
    
    def reports_menu(self):
        """قائمة التقارير"""
        print("\n📊 التقارير")
        print("هذه الميزة ستكون متوفرة قريباً...")
    
    def users_menu(self):
        """قائمة إدارة المستخدمين"""
        print("\n👤 إدارة المستخدمين")
        print("هذه الميزة ستكون متوفرة قريباً...")
    
    def backup_menu(self):
        """قائمة النسخ الاحتياطية"""
        while True:
            print("\n💾 النسخ الاحتياطية")
            print("1. إنشاء نسخة احتياطية")
            print("2. عرض النسخ الاحتياطية")
            print("0. العودة للقائمة الرئيسية")
            
            choice = input("اختر العملية: ").strip()
            
            if choice == "1":
                self.create_backup()
            elif choice == "2":
                self.list_backups()
            elif choice == "0":
                break
            else:
                print("❌ اختيار غير صحيح")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            from utils.backup_manager import backup_manager
            
            print("💾 جاري إنشاء نسخة احتياطية...")
            backup_path = backup_manager.create_database_backup()
            print(f"✅ تم إنشاء النسخة الاحتياطية: {backup_path}")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
    
    def list_backups(self):
        """عرض النسخ الاحتياطية"""
        try:
            from utils.backup_manager import backup_manager
            
            backups = backup_manager.get_backup_list()
            
            if not backups:
                print("💾 لا توجد نسخ احتياطية")
                return
            
            print(f"\n💾 النسخ الاحتياطية ({len(backups)} نسخة)")
            print("-" * 80)
            print(f"{'الاسم':<30} {'النوع':<10} {'التاريخ':<20} {'الحجم':<10}")
            print("-" * 80)
            
            for backup in backups:
                size_str = backup_manager.format_file_size(backup['size'])
                date_str = backup['created_at'].strftime('%Y-%m-%d %H:%M')
                print(f"{backup['name']:<30} {backup['type']:<10} {date_str:<20} {size_str:<10}")
                
        except Exception as e:
            print(f"❌ خطأ في عرض النسخ الاحتياطية: {e}")
    
    def run(self):
        """تشغيل البرنامج"""
        print("🚀 مرحباً بك في برنامج الوعد الصادق 4")
        print("نسخة سطر الأوامر")
        
        # محاولة تسجيل الدخول
        max_attempts = 3
        attempts = 0
        
        while attempts < max_attempts:
            if self.login():
                break
            attempts += 1
            if attempts < max_attempts:
                print(f"⚠️ المحاولة {attempts}/{max_attempts}")
        
        if not self.current_user:
            print("❌ تم تجاوز عدد المحاولات المسموحة")
            return
        
        # عرض القائمة الرئيسية
        self.show_main_menu()

def main():
    """النقطة الرئيسية لتشغيل البرنامج"""
    try:
        app = ConsoleApp()
        app.run()
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف البرنامج")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
