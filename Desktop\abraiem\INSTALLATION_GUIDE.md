# دليل تثبيت وتشغيل برنامج الوعد الصادق 4

## 🚀 مرحباً بك في برنامج الوعد الصادق 4

هذا دليل شامل لتثبيت وتشغيل برنامج الوعد الصادق 4 - نظام إدارة المبيعات والتقسيط.

## 📋 متطلبات النظام

### الحد الأدنى:
- **نظام التشغيل:** Windows 7 أو أحدث
- **Python:** الإصدار 3.6 أو أحدث
- **الذاكرة:** 4 جيجابايت RAM
- **مساحة القرص:** 500 ميجابايت

### المستحسن:
- **نظام التشغيل:** Windows 10 أو أحدث  
- **Python:** الإصدار 3.8 أو أحدث
- **الذاكرة:** 8 جيجابايت RAM
- **مساحة القرص:** 2 جيجابايت

## 🔧 خطوات التثبيت

### الخطوة 1: تثبيت Python

1. **تحميل Python:**
   - اذهب إلى الموقع الرسمي: https://www.python.org/downloads/
   - حمل أحدث إصدار من Python 3

2. **تثبيت Python:**
   - شغل ملف التثبيت
   - ✅ **مهم:** تأكد من تفعيل خيار "Add Python to PATH"
   - اختر "Install Now"

3. **التحقق من التثبيت:**
   ```cmd
   python --version
   ```
   يجب أن تظهر رسالة مثل: `Python 3.x.x`

### الخطوة 2: تثبيت المتطلبات

1. **افتح Command Prompt كمدير:**
   - اضغط `Win + R`
   - اكتب `cmd`
   - اضغط `Ctrl + Shift + Enter`

2. **انتقل إلى مجلد البرنامج:**
   ```cmd
   cd Desktop\abraiem
   ```

3. **تثبيت المتطلبات:**
   ```cmd
   python -m pip install --upgrade pip
   python -m pip install -r requirements.txt
   ```

### الخطوة 3: تشغيل البرنامج

#### الطريقة الأولى: الواجهة الرسومية (مستحسن)
```cmd
python main.py
```

#### الطريقة الثانية: سطر الأوامر (للاختبار)
```cmd
python console_app.py
```

#### الطريقة الثالثة: الاختبار البسيط
```cmd
python simple_test.py
```

## 🔐 بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

⚠️ **تنبيه مهم:** يُنصح بشدة بتغيير كلمة المرور الافتراضية فور تسجيل الدخول الأول.

## 🛠️ حل المشاكل الشائعة

### مشكلة: "python is not recognized"
**السبب:** Python غير مضاف إلى PATH
**الحل:**
1. أعد تثبيت Python مع تفعيل "Add to PATH"
2. أو أضف Python يدوياً إلى متغيرات البيئة

### مشكلة: "No module named pip"
**الحل:**
```cmd
python -m ensurepip --upgrade
```

### مشكلة: خطأ في تثبيت PyQt5
**الحل البديل:**
```cmd
python -m pip install PyQt5 --user
```

### مشكلة: "Permission denied"
**الحل:**
- شغل Command Prompt كمدير
- أو استخدم:
```cmd
python -m pip install --user -r requirements.txt
```

## 📁 هيكل الملفات

```
الوعد الصادق 4/
├── main.py                    # الملف الرئيسي (واجهة رسومية)
├── console_app.py             # نسخة سطر الأوامر
├── simple_test.py             # اختبار بسيط
├── install.py                 # سكريبت التثبيت التلقائي
├── requirements.txt           # متطلبات Python
├── README.md                  # دليل المستخدم
├── INSTALLATION_GUIDE.md      # دليل التثبيت (هذا الملف)
├── config/                    # ملفات الإعدادات
├── database/                  # إدارة قاعدة البيانات
├── models/                    # نماذج البيانات
├── ui/                        # واجهات المستخدم
├── utils/                     # أدوات مساعدة
├── data/                      # قاعدة البيانات (يتم إنشاؤها تلقائياً)
├── backups/                   # النسخ الاحتياطية
├── reports/                   # التقارير المحفوظة
└── resources/                 # الصور والأيقونات
```

## 🚀 طرق التشغيل المختلفة

### 1. التشغيل العادي
```cmd
python main.py
```

### 2. التشغيل مع تسجيل الأخطاء
```cmd
python main.py > log.txt 2>&1
```

### 3. التشغيل في الخلفية
```cmd
start python main.py
```

## 🔧 إعدادات متقدمة

### تخصيص قاعدة البيانات
يمكنك تعديل مسار قاعدة البيانات في ملف `config/settings.py`:
```python
DATABASE_PATH = "مسار/مخصص/للبيانات.db"
```

### تخصيص النسخ الاحتياطية
```python
BACKUP_DIR = "مسار/مخصص/للنسخ/الاحتياطية"
```

## 📊 ميزات البرنامج

### ✅ المميزات المتوفرة:
- ✅ تسجيل دخول آمن
- ✅ إدارة المستخدمين والصلاحيات
- ✅ إدارة المنتجات والمخزون
- ✅ إدارة العملاء
- ✅ نظام المبيعات (نقدي وتقسيط)
- ✅ إدارة الأقساط والمدفوعات
- ✅ إنشاء الفواتير PDF
- ✅ النسخ الاحتياطية
- ✅ التقارير والإحصائيات
- ✅ دعم اللغة العربية

### 🔄 المميزات قيد التطوير:
- 🔄 واجهة رسومية محسنة
- 🔄 تقارير متقدمة
- 🔄 تصدير البيانات Excel
- 🔄 إشعارات الأقساط المستحقة
- 🔄 نظام الباركود

## 🆘 الدعم الفني

### في حالة وجود مشاكل:

1. **تحقق من سجل الأخطاء:**
   ```cmd
   python main.py > error_log.txt 2>&1
   ```

2. **شغل الاختبار البسيط:**
   ```cmd
   python simple_test.py
   ```

3. **تحقق من إصدار Python:**
   ```cmd
   python --version
   pip --version
   ```

4. **تحقق من المتطلبات:**
   ```cmd
   python -c "import sqlite3; print('SQLite OK')"
   python -c "import PyQt5; print('PyQt5 OK')"
   ```

### معلومات الاتصال:
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +966 XX XXX XXXX

## 📝 ملاحظات مهمة

1. **النسخ الاحتياطية:** يُنصح بإنشاء نسخة احتياطية يومياً
2. **كلمات المرور:** استخدم كلمات مرور قوية
3. **التحديثات:** تحقق من التحديثات بانتظام
4. **الأمان:** لا تشارك بيانات تسجيل الدخول

## 🎉 تهانينا!

إذا وصلت إلى هنا، فقد تم تثبيت البرنامج بنجاح! 

**للبدء:**
1. شغل البرنامج: `python main.py`
2. سجل الدخول بالبيانات الافتراضية
3. غير كلمة المرور
4. ابدأ في إدخال بياناتك

**نتمنى لك تجربة ممتعة مع برنامج الوعد الصادق 4! 🚀**
