# -*- coding: utf-8 -*-
"""
نموذج العملاء لبرنامج الوعد الصادق 4
"""

from database.database_manager import db_manager

class CustomerModel:
    def __init__(self):
        self.db = db_manager
    
    def create_customer(self, name, phone=None, address=None, national_id=None, 
                       email=None, notes=None, credit_limit=0):
        """إنشاء عميل جديد"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO customers (name, phone, address, national_id, email, notes, credit_limit)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (name, phone, address, national_id, email, notes, credit_limit))
            
            customer_id = cursor.lastrowid
            conn.commit()
            conn.close()
            return customer_id
        except Exception as e:
            conn.close()
            raise Exception(f"فشل في إنشاء العميل: {str(e)}")
    
    def get_all_customers(self, active_only=True):
        """الحصول على جميع العملاء"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        query = '''
            SELECT id, name, phone, address, national_id, email, notes, 
                   credit_limit, is_active, created_at, updated_at
            FROM customers
        '''
        
        if active_only:
            query += " WHERE is_active = 1"
        
        query += " ORDER BY name"
        
        cursor.execute(query)
        customers = cursor.fetchall()
        conn.close()
        return customers
    
    def get_customer_by_id(self, customer_id):
        """الحصول على عميل بواسطة المعرف"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, name, phone, address, national_id, email, notes, 
                   credit_limit, is_active, created_at, updated_at
            FROM customers 
            WHERE id = ?
        ''', (customer_id,))
        
        customer = cursor.fetchone()
        conn.close()
        return customer
    
    def update_customer(self, customer_id, name=None, phone=None, address=None, 
                       national_id=None, email=None, notes=None, credit_limit=None, 
                       is_active=None):
        """تحديث بيانات العميل"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        updates = []
        params = []
        
        if name is not None:
            updates.append("name = ?")
            params.append(name)
        if phone is not None:
            updates.append("phone = ?")
            params.append(phone)
        if address is not None:
            updates.append("address = ?")
            params.append(address)
        if national_id is not None:
            updates.append("national_id = ?")
            params.append(national_id)
        if email is not None:
            updates.append("email = ?")
            params.append(email)
        if notes is not None:
            updates.append("notes = ?")
            params.append(notes)
        if credit_limit is not None:
            updates.append("credit_limit = ?")
            params.append(credit_limit)
        if is_active is not None:
            updates.append("is_active = ?")
            params.append(is_active)
        
        if updates:
            updates.append("updated_at = CURRENT_TIMESTAMP")
            params.append(customer_id)
            
            query = f"UPDATE customers SET {', '.join(updates)} WHERE id = ?"
            cursor.execute(query, params)
            conn.commit()
        
        conn.close()
    
    def delete_customer(self, customer_id):
        """حذف العميل (تعطيل فقط)"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE customers 
            SET is_active = 0, updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        ''', (customer_id,))
        
        conn.commit()
        conn.close()
    
    def search_customers(self, search_term):
        """البحث في العملاء"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, name, phone, address, national_id, email, credit_limit
            FROM customers 
            WHERE is_active = 1 AND (
                name LIKE ? OR 
                phone LIKE ? OR 
                national_id LIKE ? OR
                email LIKE ?
            )
            ORDER BY name
        ''', (f"%{search_term}%", f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"))
        
        customers = cursor.fetchall()
        conn.close()
        return customers
    
    def get_customer_installment_summary(self, customer_id):
        """الحصول على ملخص أقساط العميل"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT 
                COUNT(DISTINCT s.id) as total_sales,
                SUM(s.final_amount) as total_amount,
                SUM(s.remaining_amount) as remaining_amount,
                COUNT(i.id) as total_installments,
                COUNT(CASE WHEN i.status = 'paid' THEN 1 END) as paid_installments,
                COUNT(CASE WHEN i.status = 'overdue' THEN 1 END) as overdue_installments
            FROM sales s
            LEFT JOIN installments i ON s.id = i.sale_id
            WHERE s.customer_id = ? AND s.sale_type = 'installment' AND s.status = 'active'
        ''', (customer_id,))
        
        summary = cursor.fetchone()
        conn.close()
        return summary
    
    def get_customer_overdue_installments(self, customer_id):
        """الحصول على الأقساط المتأخرة للعميل"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT i.id, i.installment_number, i.due_date, i.amount, i.paid_amount,
                   s.id as sale_id, s.sale_date
            FROM installments i
            JOIN sales s ON i.sale_id = s.id
            WHERE s.customer_id = ? AND i.status = 'overdue'
            ORDER BY i.due_date
        ''', (customer_id,))
        
        installments = cursor.fetchall()
        conn.close()
        return installments
