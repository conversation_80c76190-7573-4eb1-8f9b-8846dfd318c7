# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات لبرنامج الوعد الصادق 4
"""

import sqlite3
import os
import shutil
from datetime import datetime, timedelta
from pathlib import Path
import bcrypt
from config.settings import DATABASE_CONFIG, BACKUP_DIR

class DatabaseManager:
    def __init__(self):
        self.db_path = DATABASE_CONFIG['path']
        self.ensure_database_exists()
        
    def ensure_database_exists(self):
        """التأكد من وجود قاعدة البيانات وإنشاؤها إذا لم تكن موجودة"""
        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        if not os.path.exists(self.db_path):
            self.create_database()
            self.create_default_admin()
    
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def create_database(self):
        """إنشاء جداول قاعدة البيانات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL CHECK (role IN ('admin', 'sales')),
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
        ''')
        
        # جدول المنتجات
        cursor.execute('''
            CREATE TABLE products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                price DECIMAL(10,2) NOT NULL,
                cost_price DECIMAL(10,2),
                category TEXT,
                stock_quantity INTEGER DEFAULT 0,
                min_stock_level INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول العملاء
        cursor.execute('''
            CREATE TABLE customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                national_id TEXT,
                email TEXT,
                notes TEXT,
                credit_limit DECIMAL(10,2) DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المبيعات
        cursor.execute('''
            CREATE TABLE sales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER,
                user_id INTEGER NOT NULL,
                sale_type TEXT NOT NULL CHECK (sale_type IN ('cash', 'installment')),
                total_amount DECIMAL(10,2) NOT NULL,
                discount_amount DECIMAL(10,2) DEFAULT 0,
                final_amount DECIMAL(10,2) NOT NULL,
                down_payment DECIMAL(10,2) DEFAULT 0,
                remaining_amount DECIMAL(10,2) DEFAULT 0,
                installment_count INTEGER DEFAULT 0,
                interest_rate DECIMAL(5,2) DEFAULT 0,
                monthly_payment DECIMAL(10,2) DEFAULT 0,
                status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
                sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # جدول تفاصيل المبيعات
        cursor.execute('''
            CREATE TABLE sale_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                unit_price DECIMAL(10,2) NOT NULL,
                total_price DECIMAL(10,2) NOT NULL,
                FOREIGN KEY (sale_id) REFERENCES sales (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        # جدول الأقساط
        cursor.execute('''
            CREATE TABLE installments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER NOT NULL,
                installment_number INTEGER NOT NULL,
                due_date DATE NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                paid_amount DECIMAL(10,2) DEFAULT 0,
                payment_date TIMESTAMP,
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'overdue')),
                notes TEXT,
                FOREIGN KEY (sale_id) REFERENCES sales (id) ON DELETE CASCADE
            )
        ''')
        
        # جدول المدفوعات
        cursor.execute('''
            CREATE TABLE payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                installment_id INTEGER,
                sale_id INTEGER,
                amount DECIMAL(10,2) NOT NULL,
                payment_method TEXT DEFAULT 'cash',
                payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                received_by INTEGER NOT NULL,
                notes TEXT,
                FOREIGN KEY (installment_id) REFERENCES installments (id),
                FOREIGN KEY (sale_id) REFERENCES sales (id),
                FOREIGN KEY (received_by) REFERENCES users (id)
            )
        ''')
        
        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER NOT NULL,
                invoice_number TEXT UNIQUE NOT NULL,
                issue_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                pdf_path TEXT,
                FOREIGN KEY (sale_id) REFERENCES sales (id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def create_default_admin(self):
        """إنشاء مستخدم مدير افتراضي"""
        password = "admin123"
        password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO users (username, password_hash, full_name, role)
            VALUES (?, ?, ?, ?)
        ''', ("admin", password_hash, "المدير العام", "admin"))
        
        conn.commit()
        conn.close()
    
    def backup_database(self, backup_name=None):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        if backup_name is None:
            backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        
        backup_path = BACKUP_DIR / backup_name
        os.makedirs(BACKUP_DIR, exist_ok=True)
        
        try:
            shutil.copy2(self.db_path, backup_path)
            return str(backup_path)
        except Exception as e:
            raise Exception(f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")
    
    def restore_database(self, backup_path):
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        if not os.path.exists(backup_path):
            raise Exception("ملف النسخة الاحتياطية غير موجود")
        
        try:
            shutil.copy2(backup_path, self.db_path)
            return True
        except Exception as e:
            raise Exception(f"فشل في استعادة النسخة الاحتياطية: {str(e)}")

# إنشاء مثيل واحد من مدير قاعدة البيانات
db_manager = DatabaseManager()
