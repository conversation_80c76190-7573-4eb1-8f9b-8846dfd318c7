# -*- coding: utf-8 -*-
"""
النافذة الرئيسية لبرنامج الوعد الصادق 4
"""

import sys
from datetime import datetime
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QLabel, QPushButton, QFrame, QStackedWidget,
                            QMenuBar, QStatusBar, QMessageBox, QApplication)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QIcon
from config.settings import COMPANY_INFO

class MainWindow(QMainWindow):
    logout_requested = pyqtSignal()
    
    def __init__(self, user_data):
        super().__init__()
        self.user_data = user_data
        self.current_page = None
        self.init_ui()
        self.setup_timer()
        
    def init_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        self.setWindowTitle(f"{COMPANY_INFO['name']} - {self.user_data['full_name']}")
        self.setGeometry(100, 100, 1200, 800)
        self.setStyleSheet(self.get_stylesheet())
        
        # الحاوية الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # الشريط الجانبي
        self.create_sidebar()
        main_layout.addWidget(self.sidebar)
        
        # المنطقة الرئيسية
        self.create_main_area()
        main_layout.addWidget(self.main_area)
        
        # شريط الحالة
        self.create_status_bar()
        
        # عرض الصفحة الرئيسية
        self.show_dashboard()
        
    def create_sidebar(self):
        """إنشاء الشريط الجانبي"""
        self.sidebar = QFrame()
        self.sidebar.setObjectName("sidebar")
        self.sidebar.setFixedWidth(250)
        
        sidebar_layout = QVBoxLayout(self.sidebar)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)
        
        # رأس الشريط الجانبي
        header = QFrame()
        header.setObjectName("sidebarHeader")
        header.setFixedHeight(80)
        
        header_layout = QVBoxLayout(header)
        header_layout.setAlignment(Qt.AlignCenter)
        
        # اسم البرنامج
        app_name = QLabel(COMPANY_INFO['name'])
        app_name.setObjectName("appName")
        app_name.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(app_name)
        
        # اسم المستخدم
        user_name = QLabel(f"مرحباً، {self.user_data['full_name']}")
        user_name.setObjectName("userName")
        user_name.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(user_name)
        
        sidebar_layout.addWidget(header)
        
        # قائمة التنقل
        nav_frame = QFrame()
        nav_frame.setObjectName("navFrame")
        nav_layout = QVBoxLayout(nav_frame)
        nav_layout.setContentsMargins(10, 20, 10, 10)
        nav_layout.setSpacing(5)
        
        # أزرار التنقل
        self.nav_buttons = {}
        
        # الصفحة الرئيسية
        self.nav_buttons['dashboard'] = self.create_nav_button("🏠", "الصفحة الرئيسية", self.show_dashboard)
        nav_layout.addWidget(self.nav_buttons['dashboard'])
        
        # إدارة المنتجات
        self.nav_buttons['products'] = self.create_nav_button("📦", "إدارة المنتجات", self.show_products)
        nav_layout.addWidget(self.nav_buttons['products'])
        
        # إدارة العملاء
        self.nav_buttons['customers'] = self.create_nav_button("👥", "إدارة العملاء", self.show_customers)
        nav_layout.addWidget(self.nav_buttons['customers'])
        
        # المبيعات
        self.nav_buttons['sales'] = self.create_nav_button("💰", "المبيعات", self.show_sales)
        nav_layout.addWidget(self.nav_buttons['sales'])
        
        # الأقساط
        self.nav_buttons['installments'] = self.create_nav_button("📅", "إدارة الأقساط", self.show_installments)
        nav_layout.addWidget(self.nav_buttons['installments'])
        
        # التقارير
        self.nav_buttons['reports'] = self.create_nav_button("📊", "التقارير", self.show_reports)
        nav_layout.addWidget(self.nav_buttons['reports'])
        
        # إدارة المستخدمين (للمدير فقط)
        if self.user_data['role'] == 'admin':
            self.nav_buttons['users'] = self.create_nav_button("👤", "إدارة المستخدمين", self.show_users)
            nav_layout.addWidget(self.nav_buttons['users'])
        
        # الإعدادات
        self.nav_buttons['settings'] = self.create_nav_button("⚙️", "الإعدادات", self.show_settings)
        nav_layout.addWidget(self.nav_buttons['settings'])
        
        nav_layout.addStretch()
        
        # زر تسجيل الخروج
        logout_btn = self.create_nav_button("🚪", "تسجيل الخروج", self.handle_logout)
        logout_btn.setObjectName("logoutButton")
        nav_layout.addWidget(logout_btn)
        
        sidebar_layout.addWidget(nav_frame)
        
    def create_nav_button(self, icon, text, callback):
        """إنشاء زر تنقل"""
        button = QPushButton(f"{icon}  {text}")
        button.setObjectName("navButton")
        button.clicked.connect(callback)
        return button
        
    def create_main_area(self):
        """إنشاء المنطقة الرئيسية"""
        self.main_area = QFrame()
        self.main_area.setObjectName("mainArea")
        
        main_layout = QVBoxLayout(self.main_area)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # شريط علوي
        top_bar = QFrame()
        top_bar.setObjectName("topBar")
        top_bar.setFixedHeight(60)
        
        top_layout = QHBoxLayout(top_bar)
        top_layout.setContentsMargins(20, 10, 20, 10)
        
        # عنوان الصفحة
        self.page_title = QLabel("الصفحة الرئيسية")
        self.page_title.setObjectName("pageTitle")
        top_layout.addWidget(self.page_title)
        
        top_layout.addStretch()
        
        # التاريخ والوقت
        self.datetime_label = QLabel()
        self.datetime_label.setObjectName("datetimeLabel")
        top_layout.addWidget(self.datetime_label)
        
        main_layout.addWidget(top_bar)
        
        # منطقة المحتوى
        self.content_stack = QStackedWidget()
        self.content_stack.setObjectName("contentStack")
        main_layout.addWidget(self.content_stack)
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # معلومات المستخدم
        user_info = f"المستخدم: {self.user_data['full_name']} | الصلاحية: {self.get_role_name(self.user_data['role'])}"
        self.status_bar.showMessage(user_info)
        
    def get_role_name(self, role):
        """الحصول على اسم الصلاحية بالعربية"""
        roles = {
            'admin': 'مدير',
            'sales': 'موظف مبيعات'
        }
        return roles.get(role, role)
        
    def setup_timer(self):
        """إعداد مؤقت تحديث التاريخ والوقت"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_datetime)
        self.timer.start(1000)  # تحديث كل ثانية
        self.update_datetime()
        
    def update_datetime(self):
        """تحديث التاريخ والوقت"""
        now = datetime.now()
        datetime_str = now.strftime("%Y/%m/%d - %H:%M:%S")
        self.datetime_label.setText(datetime_str)
        
    def set_active_nav_button(self, button_name):
        """تعيين الزر النشط في التنقل"""
        for name, button in self.nav_buttons.items():
            if name == button_name:
                button.setProperty("active", True)
            else:
                button.setProperty("active", False)
            button.style().unpolish(button)
            button.style().polish(button)
            
    def show_dashboard(self):
        """عرض الصفحة الرئيسية"""
        self.page_title.setText("الصفحة الرئيسية")
        self.set_active_nav_button('dashboard')
        # TODO: إضافة محتوى الصفحة الرئيسية
        
    def show_products(self):
        """عرض صفحة المنتجات"""
        self.page_title.setText("إدارة المنتجات")
        self.set_active_nav_button('products')
        # TODO: إضافة محتوى صفحة المنتجات
        
    def show_customers(self):
        """عرض صفحة العملاء"""
        self.page_title.setText("إدارة العملاء")
        self.set_active_nav_button('customers')
        # TODO: إضافة محتوى صفحة العملاء
        
    def show_sales(self):
        """عرض صفحة المبيعات"""
        self.page_title.setText("المبيعات")
        self.set_active_nav_button('sales')
        # TODO: إضافة محتوى صفحة المبيعات
        
    def show_installments(self):
        """عرض صفحة الأقساط"""
        self.page_title.setText("إدارة الأقساط")
        self.set_active_nav_button('installments')
        # TODO: إضافة محتوى صفحة الأقساط
        
    def show_reports(self):
        """عرض صفحة التقارير"""
        self.page_title.setText("التقارير")
        self.set_active_nav_button('reports')
        # TODO: إضافة محتوى صفحة التقارير
        
    def show_users(self):
        """عرض صفحة المستخدمين"""
        self.page_title.setText("إدارة المستخدمين")
        self.set_active_nav_button('users')
        # TODO: إضافة محتوى صفحة المستخدمين
        
    def show_settings(self):
        """عرض صفحة الإعدادات"""
        self.page_title.setText("الإعدادات")
        self.set_active_nav_button('settings')
        # TODO: إضافة محتوى صفحة الإعدادات
        
    def handle_logout(self):
        """معالجة تسجيل الخروج"""
        reply = QMessageBox.question(self, "تسجيل الخروج", 
                                   "هل أنت متأكد من تسجيل الخروج؟",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.logout_requested.emit()
            self.close()
            
    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        reply = QMessageBox.question(self, "إغلاق البرنامج", 
                                   "هل أنت متأكد من إغلاق البرنامج؟",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()
            
    def get_stylesheet(self):
        """تنسيق CSS للنافذة الرئيسية"""
        return """
            QMainWindow {
                background-color: #f5f5f5;
            }
            
            #sidebar {
                background-color: #2c3e50;
                border-right: 1px solid #34495e;
            }
            
            #sidebarHeader {
                background-color: #34495e;
                border-bottom: 1px solid #4a5f7a;
            }
            
            #appName {
                font-size: 18px;
                font-weight: bold;
                color: white;
            }
            
            #userName {
                font-size: 12px;
                color: #bdc3c7;
            }
            
            #navFrame {
                background-color: #2c3e50;
            }
            
            #navButton {
                background-color: transparent;
                color: #ecf0f1;
                border: none;
                padding: 15px 20px;
                text-align: right;
                font-size: 14px;
                border-radius: 5px;
                margin: 2px 0;
            }
            
            #navButton:hover {
                background-color: #34495e;
            }
            
            #navButton[active="true"] {
                background-color: #3498db;
                color: white;
            }
            
            #logoutButton {
                background-color: #e74c3c;
                color: white;
            }
            
            #logoutButton:hover {
                background-color: #c0392b;
            }
            
            #mainArea {
                background-color: white;
            }
            
            #topBar {
                background-color: white;
                border-bottom: 1px solid #ddd;
            }
            
            #pageTitle {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
            }
            
            #datetimeLabel {
                font-size: 14px;
                color: #7f8c8d;
            }
            
            #contentStack {
                background-color: #f8f9fa;
            }
        """
