# -*- coding: utf-8 -*-
"""
اختبار أساسي لبرنامج الوعد الصادق 4 بدون واجهة رسومية
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_database():
    """اختبار قاعدة البيانات"""
    try:
        print("🔍 اختبار قاعدة البيانات...")
        
        from database.database_manager import db_manager
        print("✅ تم إنشاء قاعدة البيانات بنجاح")
        
        # اختبار الاتصال
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # اختبار جدول المستخدمين
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"✅ عدد المستخدمين في قاعدة البيانات: {user_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_models():
    """اختبار النماذج"""
    try:
        print("\n🔍 اختبار النماذج...")
        
        # اختبار نموذج المستخدم
        from models.user_model import UserModel
        user_model = UserModel()
        
        # محاولة تسجيل الدخول بالمستخدم الافتراضي
        user = user_model.authenticate_user("admin", "admin123")
        if user:
            print(f"✅ تسجيل دخول ناجح: {user['full_name']} ({user['role']})")
        else:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        # اختبار نموذج المنتجات
        from models.product_model import ProductModel
        product_model = ProductModel()
        
        # إضافة منتج تجريبي
        product_id = product_model.create_product(
            name="منتج تجريبي",
            description="هذا منتج للاختبار",
            price=100.0,
            stock_quantity=10
        )
        print(f"✅ تم إنشاء منتج تجريبي برقم: {product_id}")
        
        # اختبار نموذج العملاء
        from models.customer_model import CustomerModel
        customer_model = CustomerModel()
        
        customer_id = customer_model.create_customer(
            name="عميل تجريبي",
            phone="0501234567",
            address="الرياض"
        )
        print(f"✅ تم إنشاء عميل تجريبي برقم: {customer_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في النماذج: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sales():
    """اختبار نظام المبيعات"""
    try:
        print("\n🔍 اختبار نظام المبيعات...")
        
        from models.sales_model import SalesModel
        sales_model = SalesModel()
        
        # إنشاء عملية بيع تجريبية
        items = [
            {
                'product_id': 1,
                'quantity': 2,
                'unit_price': 100.0
            }
        ]
        
        sale_id = sales_model.create_sale(
            customer_id=1,
            user_id=1,
            sale_type='cash',
            items=items,
            discount_amount=10.0
        )
        
        print(f"✅ تم إنشاء عملية بيع برقم: {sale_id}")
        
        # اختبار استرجاع بيانات البيع
        sale_data = sales_model.get_sale_by_id(sale_id)
        if sale_data:
            print(f"✅ تم استرجاع بيانات البيع: {sale_data['final_amount']} ريال")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نظام المبيعات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backup():
    """اختبار النسخ الاحتياطية"""
    try:
        print("\n🔍 اختبار النسخ الاحتياطية...")
        
        from utils.backup_manager import backup_manager
        
        # إنشاء نسخة احتياطية
        backup_path = backup_manager.create_database_backup()
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        
        # عرض قائمة النسخ الاحتياطية
        backups = backup_manager.get_backup_list()
        print(f"✅ عدد النسخ الاحتياطية: {len(backups)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في النسخ الاحتياطية: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🚀 اختبار برنامج الوعد الصادق 4")
    print("=" * 60)
    
    # إنشاء المجلدات المطلوبة
    from config.settings import create_directories
    create_directories()
    print("✅ تم إنشاء المجلدات المطلوبة")
    
    # تشغيل الاختبارات
    tests = [
        ("قاعدة البيانات", test_database),
        ("النماذج", test_models),
        ("نظام المبيعات", test_sales),
        ("النسخ الاحتياطية", test_backup)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ اختبار {test_name} نجح")
        else:
            print(f"❌ اختبار {test_name} فشل")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! البرنامج جاهز للاستخدام")
        print("\n📋 بيانات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("\n🚀 لتشغيل البرنامج مع الواجهة الرسومية:")
        print("   تأكد من تثبيت PyQt5 أولاً:")
        print("   pip install PyQt5")
        print("   ثم شغل: python main.py")
    else:
        print("❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
